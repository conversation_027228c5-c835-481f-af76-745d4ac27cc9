# Care Canvas Implementation - Comprehensive Summary

## Project Context

**Project**: GenNext - AI-powered healthcare management platform
**Component**: Care Canvas - A comprehensive healthcare dashboard component that provides members with quick access to their care management tools, appointments, prescriptions, claims, and health plan information.

The Care Canvas serves as a central hub for healthcare interactions, integrating with an AI chat system to enable conversational healthcare management. It's designed to work both as a standalone component and within the broader journeys/chat interface.

## Design Requirements

Based on the provided design mockup, the Care Canvas required:

### Layout & Structure
- Collapsible card-based sections with consistent styling
- Purple/pink accent colors throughout the design
- Compact, mobile-friendly layout with proper spacing
- Dark mode optimization with `dark:` classnames
- Component ordering to match the design hierarchy

### Functionality Requirements
- All sections must be collapsible (closed by default)
- Integration with existing chat system for user actions
- AI function calling for dynamic content population
- localStorage persistence for certain data (Focus appointments)
- Placeholder functionality for future features

## Technical Implementation

### Components Created/Modified

#### 1. **CareCanvas.tsx** (Main Component)
- Complete rewrite to match design specifications
- Added collapsible state management for all sections
- Integrated with multiple hooks for data and actions
- Implemented dark mode styling throughout
- Added proper component ordering and responsive design

#### 2. **useCareCanvasActions.ts** (Hook)
- Enhanced existing hook with new message handlers:
  - `handleMedicationQuestion()` - "I have a question about my current medications"
  - `handleMedicationReminders()` - "I'd like to setup medication reminders"
  - Updated `handleAppointmentRequest()` to support generic scheduling
- All handlers integrate with existing chat thread creation system

#### 3. **useFocusAppointments.ts** (New Hook)
- Created for managing Focus component appointments
- localStorage integration with key: `care-canvas-focus-appointments`
- CRUD operations for appointment management
- TypeScript interface for appointment data structure

### AI Assistant Integration

#### New Function Declaration: `add_appointment_to_focus`
```typescript
{
  name: 'add_appointment_to_focus',
  description: 'Adds scheduled appointments to Focus section (provider appointments only, NOT nurse line calls)',
  parameters: {
    type: 'telehealth' | 'in-person',
    provider: string,
    specialty: string,
    date: string,
    time: string
  }
}
```

#### System Prompt Updates
1. **Terminology Change**: "Coordinated Care Program" → "Dedicated Nurse Line" throughout
2. **Focus Component Instructions**: Added guidance for when to populate Focus with appointments
3. **Medication Reminders Flow**: Added conversational flow for SMS/push notification preferences

#### Chat Integration
- Updated `ChatView.tsx` to handle `add_appointment_to_focus` function calls
- localStorage integration for persistent appointment storage
- Function result rendering (hidden span elements)

## Component Details

### 1. Focus Section
- **Purpose**: Display upcoming appointments and focus items
- **Data**: Appointments from localStorage via `useFocusAppointments` hook
- **Empty State**: "No items to focus on right now" (generic, not appointment-specific)
- **AI Integration**: Populated via `add_appointment_to_focus` function calls
- **Buttons**:
  - Telehealth: "Join Now" → triggers Fast Forward flow
  - In-Person: "Directions" → placeholder functionality

### 2. Care Team Section
- **Data Displayed**: Name, Specialty, PCP badge only (removed practice, location, role)
- **Filtering**: Uses existing profile.careTeam data
- **Button Layout**: "Schedule" button positioned on right side of each card
- **Additional Buttons**:
  - "View Full Care Team" (placeholder)
  - "Find Care" (chat integration)
- **Chat Integration**: Generic appointment scheduling (AI asks for in-person vs telehealth preference)

### 3. Prescriptions Section
- **Data Displayed**: Member name, medication name, brand name, frequency, refills remaining
- **Filtering**: Excludes supplements, vitamins, calcium, and Ethan's prescriptions
- **Removed Data**: Tier, dosage, last fill date
- **Buttons**:
  - "Refill" (existing functionality)
  - "View All Prescriptions" (placeholder)
  - "Ask about my medications" (chat integration)
  - "Setup Medication Reminders" (chat integration with SMS/push flow)

### 4. Plan Usage Section (New)
- **Data Source**: `profile.planUsage`
- **Components**:
  - Deductible progress bar (individual)
  - Out-of-Pocket Max progress bar (individual)
  - Progress percentages and amounts
- **Button**: "Optimize my costs" (placeholder, integrated into this section per user request)
- **Styling**: Green accent color, dark mode optimized

### 5. Claims Section
- **Data Displayed**: Recent claims (limited to 3), procedure description, provider, date, amount, status
- **Field Mapping**: Uses `claimDate`, `procedureDescription`, `billedAmount`, `claimStatus`
- **Buttons**:
  - "Ask about this claim" (chat integration with provider/date context)
  - "View All Claims" (placeholder)

### 6. Condition Management Section
- **Structure**: Individual cards for each service with descriptions
- **Services**:
  - **Dedicated Nurse Line**: Purple theme, "Schedule Call Back" button
  - **Telehealth**: Blue theme, "Schedule Telehealth" button
  - **Symptom Logger**: Green theme, "Open Logger" button (placeholder)
- **Chat Integration**: Dedicated nurse line and telehealth scheduling

### 7. Wellness & Prevention Section
- **Content**: Sample wellness items as shown in design
- **Items**:
  - "Personalized Exercise Plan" with "View Plan" button
  - "Prevent Chronic Flu Reminders" with "Set Reminders" button
- **Styling**: Teal accent color, placeholder functionality

## Key Features Implemented

### AI Function Calling System
- Focus component populated dynamically by AI after appointment scheduling
- Distinction between provider appointments (added to Focus) vs nurse line calls (not added)
- Integration with existing `enrollInConditionManagementFunctionDeclaration` pattern

### localStorage Integration
- Persistent storage for Focus appointments
- Real-time updates when AI adds new appointments
- Proper data structure with IDs, timestamps, and appointment details

### Chat Message Handlers
- Comprehensive set of handlers for all user actions
- Consistent thread naming and message formatting
- Integration with existing chat context and thread management

### Medication Reminders Flow
- Conversational setup process in system prompt
- User preference collection (SMS, push notifications, or both)
- Confirmation messaging for completed setup

## User Preferences & Architectural Patterns

### Styling Preferences
- **Dark Mode Priority**: User emphasizes optimizing for dark mode with `dark:` classnames
- **Color Consistency**: Each component type has its own color theme for visual hierarchy
- **Button Positioning**: Schedule buttons on right side, consistent with modern UI patterns

### Component Behavior
- **Collapsible Sections**: Closed by default, user preference established
- **Minimal Data Display**: Show only essential information, avoid information overload
- **Generic Empty States**: Avoid content-specific empty state messages

### Architectural Patterns
- **Scalable Components**: Built for future AI function calling and programmatic content addition
- **Cross-page Navigation**: Integration with journeys page for chat functionality
- **Consistent Integration**: Follow existing patterns for chat messaging and thread creation
- **localStorage Persistence**: For dynamic content that needs to persist across sessions

### Integration Philosophy
- **Chat-First Approach**: Most actions should integrate with chat system rather than standalone functionality
- **AI Function Calling**: Prefer AI-driven content population over static displays
- **Placeholder Strategy**: Implement UI elements for future functionality rather than omitting them

## Development Notes

### Component Order (Final)
1. Focus
2. Care Team
3. Prescriptions
4. Plan Usage
5. Claims
6. Condition Management
7. Wellness & Prevention

### Dark Mode Implementation
- Comprehensive `dark:` classnames for all interactive elements
- Color-coded hover states: `dark:hover:bg-{color}-900/20`
- Proper text contrast: `dark:text-{color}-400 dark:hover:text-{color}-300`
- Border styling: `dark:border-{color}-600`

This implementation provides a solid foundation for the Care Canvas component that can be extended with additional AI function calling capabilities and enhanced user interactions as the platform evolves.