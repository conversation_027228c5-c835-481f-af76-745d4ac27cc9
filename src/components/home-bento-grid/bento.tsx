import {
    <PERSON><PERSON><PERSON>,
    Pill,
    Stethoscope,
    WalletCards,
    ClipboardList
} from "lucide-react";
import { BentoGrid, BentoGridItem } from "@/components/ui/bento-grid";

const backgroundClass = 
"absolute inset-0 w-full h-[100px] object-cover rounded-3xl transition-all duration-300 ease-out  group-hover:scale-105 ";


const features = [
    {
      Icon: HeartPulse,
      name: "Your Coordinated Care Program",
      description: "Get dedicated support for <PERSON><PERSON><PERSON>'s disease from a specialized nurse.",
      href: "/?prompt=Tell%20me%20more%20about%20the%20Coordinated%20Care%20Program",
      background: <img src="/images/bento/coordinated-care.jpeg" className={backgroundClass} alt="Coordinated Care Program visualization" />,
      className: "lg:row-start-1 lg:row-end-4 lg:col-start-2 lg:col-end-3 ",
    },
    {
      Icon: Pill,
      name: "Manage Your Prescriptions",
      description: "Review your medications, check refill status, and explore delivery options.",
      href: "/profile",
      background: <img src="/images/bento/prescriptions.jpeg" className={backgroundClass} alt="Prescriptions visualization" />,
      className: "lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3",
    },
    {
      Icon: Stethoscope,
      name: "Find Care",
      description: "Search for doctors, specialists, and facilities in your network.",
      href: "/?prompt=Can you help me find care?",
      background: <img src="/images/bento/findcare.jpeg" className={backgroundClass} alt="Find Care visualization" />,
      className: "lg:col-start-1 lg:col-end-3 lg:row-start-3 lg:row-end-4",
    },
    {
      Icon: WalletCards,
      name: "View Plan Spending",
      description: "Track your deductibles and out-of-pocket spending.",
      href: "/profile#plan-usage",
      background: <img src="/images/bento/spending.jpeg" className={backgroundClass} alt="Plan Spending visualization" />,
      className: "md:col-span-1",
    },
    {
      Icon: ClipboardList,
      name: "Explore Rx Options & Benefits",
      description:
        "Understand your medication options, effects, and costs.",
      href: "/?prompt=Tell%20me%20about%20my%20Rx%20options",
      background: <img src="/images/bento/benefits.jpeg" className={backgroundClass} alt="Rx Options & Benefits visualization" />,
      className: "md:col-span-2",
    },
  ];
  
export function Bento() {
  return (
    <BentoGrid className="lg:grid-rows-3">
      {features.map((feature, i) => (
        <BentoGridItem 
            key={feature.name} 
            title={feature.name}
            description={feature.description}
            header={feature.background}
            icon={<feature.Icon className="h-4 w-4 text-neutral-500" />}
            className={i === 4 ? "md:col-span-2" : ""}
            href={feature.href}
        />
      ))}
    </BentoGrid>
  );
}
  