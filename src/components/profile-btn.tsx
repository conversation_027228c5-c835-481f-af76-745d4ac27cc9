"use client";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "./ui/button";
import { useRouter } from "next/navigation";
import { useIsMobile } from "@/hooks/use-mobile";

export function ProfileBtn() {
  const router = useRouter();
  const isMobile = useIsMobile();

  return (
    <div id="profile-btn" className="flex flex-row my-2">
      <Button
      className="bg-transparent border-none hover:bg-gray-800/50 pointer-events-auto w-full justify-start"
        onClick={() => {
          router.push('/profile');
        }}
        aria-label="Navigate to Profile page"
      >
        <span className={cn(
          "h-8 w-8 rounded-full text-[#252941] shadow-2xl bg-gradient-to-b from-[#C78fff] to-[#FF84DD] hover:scale-105 transition-all duration-300 p-1.5 text-sm my-2"
        )}>EC</span> <span className="text-sm text-zinc-800 dark:text-zinc-100 p-2"><PERSON></span>
      </Button>
    </div>
  )
}
