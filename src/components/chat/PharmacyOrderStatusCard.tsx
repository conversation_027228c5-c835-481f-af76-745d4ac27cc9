import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Pill, Phone, DollarSign, Check, Clock, Truck, Package } from "lucide-react";
import { cn } from "@/lib/utils";

interface PharmacyOrderStatus {
  medicationName: string;
  dosage: string;
  pharmacyName: string;
  pharmacyPhone: string;
  costEstimate: string;
  currentStatus: 'Received' | 'In Progress' | 'Out for Delivery' | 'Delivered';
}

const statusSteps = [
  { key: 'Received', label: 'Received', icon: Check },
  { key: 'In Progress', label: 'In Progress', icon: Clock },
  { key: 'Out for Delivery', label: 'Out for Delivery', icon: Truck },
  { key: 'Delivered', label: 'Delivered', icon: Package }
];

export default function PharmacyOrderStatusCard({ data }: { data: PharmacyOrderStatus }) {
  if (!data) {
    return <div className="text-destructive">Invalid pharmacy order data.</div>;
  }

  const currentStepIndex = statusSteps.findIndex(step => step.key === data.currentStatus);
  const progressPercentage = ((currentStepIndex + 1) / statusSteps.length) * 100;

  return (
    <Card className="shadow-md w-full max-w-none sm:max-w-md lg:max-w-lg xl:max-w-xl">
      <CardHeader className="p-4 sm:p-6">
        <CardTitle className="flex flex-col sm:flex-row sm:items-center gap-2">
          <div className="flex items-center gap-2">
            <Pill size={18} className="text-blue-600" />
            <span className="text-sm sm:text-base">{data.medicationName}</span>
          </div>
          <span className="text-sm sm:text-base text-muted-foreground">({data.dosage})</span>
        </CardTitle>
        <div className="text-cyan-600 font-medium text-sm sm:text-base">{data.pharmacyName}</div>
      </CardHeader>
      <CardContent className="space-y-4 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-6">
          <div className="flex items-center gap-2">
            <Phone size={16} />
            <span className="text-sm sm:text-base">{data.pharmacyPhone}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <DollarSign size={16} />
            <span className="text-sm sm:text-base">Cost Estimate: {data.costEstimate}</span>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <span className="text-sm sm:text-base font-medium">Order Status</span>
            <span className="text-sm sm:text-base text-muted-foreground font-medium">{data.currentStatus}</span>
          </div>
          
          <Progress value={progressPercentage} className="h-2 sm:h-3" />
          
          <div className="grid grid-cols-2 sm:flex sm:justify-between gap-4 sm:gap-2">
            {statusSteps.map((step, index) => {
              const Icon = step.icon;
              const isCompleted = index <= currentStepIndex;
              const isCurrent = index === currentStepIndex;
              
              return (
                <div key={step.key} className="flex flex-col items-center space-y-2">
                  <div
                    className={cn(
                      "w-10 h-10 sm:w-8 sm:h-8 rounded-full flex items-center justify-center border-2",
                      isCompleted
                        ? "bg-primary border-primary text-primary-foreground"
                        : "bg-background border-muted-foreground text-muted-foreground",
                      isCurrent && "ring-2 ring-primary ring-offset-2"
                    )}
                  >
                    <Icon size={16} className="sm:w-3.5 sm:h-3.5" />
                  </div>
                  <span
                    className={cn(
                      "text-xs sm:text-xs text-center max-w-[80px] sm:max-w-[60px] leading-tight",
                      isCompleted ? "text-primary font-medium" : "text-muted-foreground"
                    )}
                  >
                    {step.label}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}