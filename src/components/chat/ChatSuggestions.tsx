import React from 'react';
import { Button } from '@/components/ui/button';
import { Suggestion } from '@/types';
import { RippleButton } from '@/components/animate-ui/buttons/ripple';

interface ChatSuggestionsProps {
  suggestions: Suggestion[];
  onSuggestionClick: (suggestionText: string) => void;
}

const ChatSuggestions: React.FC<ChatSuggestionsProps> = ({ suggestions, onSuggestionClick }) => {
  return (
    <div className="flex flex-wrap gap-2 pt-2 pb-2 text-center justify-center">
      {suggestions.map((suggestion, index) => (
        <RippleButton
          key={index}
          variant="outline"
          size="sm"
          className="rounded-full bg-white whitespace-normal max-w-fit py-6 px-4"
          onClick={() => onSuggestionClick(suggestion.text)}
        >
          {suggestion.text}
        </RippleButton>
      ))}
    </div>
  );
};

export default ChatSuggestions;