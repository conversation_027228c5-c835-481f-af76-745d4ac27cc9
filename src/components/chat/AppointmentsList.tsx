import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Calendar, Clock, MapPin, User } from "lucide-react";

interface Appointment {
  id: string;
  provider: string;
  date: string;
  time: string;
  location: string;
  status: string;
}

export default function AppointmentsList({ data }: { data: Appointment[] }) {
  if (!Array.isArray(data)) {
    return <div className="text-destructive">Invalid appointments data.</div>;
  }
  return (
    <div className="grid gap-4">
      {data.map((appt) => (
        <Card key={appt.id} className="shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User size={18} /> {appt.provider}
            </CardTitle>
            <div className="text-muted-foreground text-sm">{appt.status}</div>
          </CardHeader>
          <CardContent className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <Calendar size={16} />
              <span>{appt.date}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock size={16} />
              <span>{appt.time}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin size={16} />
              <span>{appt.location}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
} 