import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';

interface TelehealthAppointmentCardProps {
  date: string;
  time: string;
  doctorName: string;
  specialty: string;
  rating: number;
  reviewsCount: number;
  costEstimate: string;
}

interface TelehealthAppointmentCardComponentProps {
  data: TelehealthAppointmentCardProps;
  onJoinNow?: () => void;
}

const TelehealthAppointmentCard: React.FC<TelehealthAppointmentCardComponentProps> = ({
  data,
  onJoinNow
}) => {
  const { date, time, doctorName, specialty, rating, reviewsCount, costEstimate } = data;
  // Function to render stars (simple text representation for now)
  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

    return (
      <>
        {'★'.repeat(fullStars)}
        {halfStar && '½'}
        {'☆'.repeat(emptyStars)}
      </>
    );
  };

  return (
    <Card className="w-full max-w-none sm:max-w-md lg:max-w-lg xl:max-w-xl">
      <CardHeader>
        <CardTitle className="text-lg mb-[-20px]">Telehealth Appointment</CardTitle>
      </CardHeader>
      <CardContent className="grid gap-4 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
          <div className="flex-1">
            <p className="text-sm sm:text-base font-medium">{date}</p>
            <p className="text-sm text-muted-foreground">{time}</p>
          </div>
          <Button
            onClick={() => onJoinNow && onJoinNow()}
            className="w-full sm:w-auto min-h-[44px] sm:min-h-[40px] text-sm sm:text-base"
          >
            Join Now
          </Button>
        </div>
        <div className="border-t pt-4">
          <h3 className="text-base sm:text-lg font-semibold">{doctorName}</h3>
          <div className="flex flex-wrap items-center gap-2 mt-2">
            <Badge variant="secondary" className="text-xs sm:text-sm">IN NETWORK</Badge>
            <Badge variant="secondary" className="text-xs sm:text-sm">TELEHEALTH</Badge>
          </div>
          <p className="text-sm sm:text-base text-muted-foreground mt-3">{specialty}</p>
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mt-3">
            <div className="flex items-center">
              <p className="text-sm font-medium mr-1">Rating:</p>
              <span className="text-yellow-500">{renderStars(rating)}</span>
              <span className="text-sm text-muted-foreground ml-2">({reviewsCount} reviews)</span>
            </div>
            <div className="flex items-center">
              <p className="text-sm font-medium mr-1">Cost Estimate:</p>
              <span className="text-sm text-muted-foreground">{costEstimate}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TelehealthAppointmentCard;