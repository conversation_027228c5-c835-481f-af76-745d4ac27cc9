'use client';

import { But<PERSON> } from '../ui/button';

interface TelehealthAppointmentTimePickerProps {
  onTimeSelect: (time: string) => void;
}

const TelehealthAppointmentTimePicker: React.FC<TelehealthAppointmentTimePickerProps> = ({ onTimeSelect }) => {
  return (
    <div className="w-full max-w-none sm:max-w-md lg:max-w-lg xl:max-w-xl">
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-2">
        <Button
          onClick={() => onTimeSelect('10:00 am')}
          className="w-full min-h-[44px] sm:min-h-[40px] text-sm sm:text-base"
        >
          10:00 am
        </Button>
        <Button
          onClick={() => onTimeSelect('10:30 am')}
          className="w-full min-h-[44px] sm:min-h-[40px] text-sm sm:text-base"
        >
          10:30 am
        </Button>
        <Button
          onClick={() => onTimeSelect('11:00 am')}
          className="w-full min-h-[44px] sm:min-h-[40px] text-sm sm:text-base"
        >
          11:00 am
        </Button>
      </div>
    </div>
  );
};

export default TelehealthAppointmentTimePicker;