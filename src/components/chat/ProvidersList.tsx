import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { MapPin, Phone, Star, DollarSign } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import type { Provider } from '@/types';


export default function ProvidersList({ data }: { data: Provider[] }) {
  if (!Array.isArray(data)) {
    return <div className="text-destructive">Invalid provider data.</div>;
  }
  return (
    <div className="grid gap-4">
      {data.map((prov) => (
        <Card key={prov.id} className="shadow-md">
          <CardHeader className="relative pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-xl font-semibold">{prov.name}</CardTitle>
              <span className="text-sm text-muted-foreground dark:text-white">{prov.distance} mi</span>
            </div>
            <Badge variant="outline" className="w-fit">IN NETWORK</Badge>
            <div className="flex items-center gap-1 text-normal font-semibold mt-2">
              <DollarSign size={18} className="text-green-600" />
              <span className="font-normal">What you pay:</span> <span className="font-semibold">{prov.cost}</span>
            </div>
          </CardHeader>
          <div className="flex items-center px-6">
            <Separator/>
          </div>
          <CardContent className="flex flex-col gap-2 pt-0">
            <div className="flex items-center gap-1 text-sm text-muted-foreground dark:text-white">
              <Star size={16} className="text-yellow-500 fill-yellow-500" />
              <span>Rating: {prov.rating} ({prov.reviewsCount} reviews)</span>
            </div>
            <div className="text-sm text-muted-foreground dark:text-white">{prov.specialty}</div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground dark:text-white">
              <MapPin size={16} />
              <span>{prov.address}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground dark:text-white">
              <Phone size={16} />
              <span>{prov.phone}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}