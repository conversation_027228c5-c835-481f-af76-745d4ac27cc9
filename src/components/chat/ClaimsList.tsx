import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Calendar, DollarSign, User, FileText } from "lucide-react";

interface Claim {
  id: string;
  date: string;
  provider: string;
  service: string;
  amount: number;
  status: string;
  memberOwes: number;
}

export default function ClaimsList({ data }: { data: Claim[] }) {
  if (!Array.isArray(data)) {
    return <div className="text-destructive">Invalid claims data.</div>;
  }
  return (
    <div className="grid gap-4">
      {data.map((claim) => (
        <Card key={claim.id} className="shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText size={18} /> {claim.service}
            </CardTitle>
            <div className="text-muted-foreground text-sm">{claim.status}</div>
          </CardHeader>
          <CardContent className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <Calendar size={16} />
              <span>{claim.date}</span>
            </div>
            <div className="flex items-center gap-2">
              <User size={16} />
              <span>{claim.provider}</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign size={16} />
              <span>Total: ${claim.amount}</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign size={16} className="text-blue-600" />
              <span className="text-blue-600">You owe: ${claim.memberOwes}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
} 