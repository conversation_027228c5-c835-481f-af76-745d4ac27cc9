import { Card, CardContent, CardHeader } from '../ui/card';
import { Progress } from '../ui/progress';
import { Skeleton } from '../ui/skeleton';

interface PlanProgressData {
  memberName: string;
  memberDob: string;
  planName: string;
  deductible: {
    total: number;
    spent: number;
    remaining: number;
  };
  outOfPocketMax: {
    total: number;
    spent: number;
    remaining: number;
  };
}

const defaultPlanData: PlanProgressData = {
  memberName: 'Not Available',
  memberDob: 'N/A',
  planName: 'Not Available',
  deductible: {
    total: 0,
    spent: 0,
    remaining: 0
  },
  outOfPocketMax: {
    total: 0,
    spent: 0,
    remaining: 0
  }
};

interface PlanProgressCardProps {
  data?: PlanProgressData;
  isLoading?: boolean;
}

const PlanProgressCard: React.FC<PlanProgressCardProps> = ({ data = defaultPlanData, isLoading = false }) => {
  if (isLoading) {
    return (
      <Card className="w-full max-w-none sm:max-w-md lg:max-w-lg xl:max-w-xl">
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent className="grid gap-4">
          <Skeleton className="h-4 w-48" />
          <div className="grid gap-2">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
            <Skeleton className="h-2 w-full" />
          </div>
          <div className="grid gap-2">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-36" />
              <Skeleton className="h-4 w-16" />
            </div>
            <Skeleton className="h-2 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const safeData = {
    memberName: data?.memberName ?? defaultPlanData.memberName,
    memberDob: data?.memberDob ?? defaultPlanData.memberDob,
    planName: data?.planName ?? defaultPlanData.planName,
    deductible: {
      total: data?.deductible?.total ?? defaultPlanData.deductible.total,
      spent: data?.deductible?.spent ?? defaultPlanData.deductible.spent,
      remaining: data?.deductible?.remaining ?? defaultPlanData.deductible.remaining,
    },
    outOfPocketMax: {
      total: data?.outOfPocketMax?.total ?? defaultPlanData.outOfPocketMax.total,
      spent: data?.outOfPocketMax?.spent ?? defaultPlanData.outOfPocketMax.spent,
      remaining: data?.outOfPocketMax?.remaining ?? defaultPlanData.outOfPocketMax.remaining,
    }
  };

  const deductibleProgress = safeData.deductible.total > 0 
    ? (safeData.deductible.spent / safeData.deductible.total) * 100
    : 0;

  const oopMaxProgress = safeData.outOfPocketMax.total > 0
    ? (safeData.outOfPocketMax.spent / safeData.outOfPocketMax.total) * 100
    : 0;

  return (
    <Card className="w-full max-w-none sm:max-w-md lg:max-w-lg xl:max-w-xl">
      <CardHeader>
        <h3 className="text-lg font-semibold">Your Plan Progress</h3>
      </CardHeader>
      <CardContent className="grid gap-4 p-4 sm:px-6 sm:pt-0">
        <div className="space-y-1">
          <p className="text-sm sm:text-base font-medium">
            {safeData.memberName}
            <span className="text-sm text-gray-500 block sm:inline sm:ml-1 dark:text-white">
              ({safeData.memberDob})
            </span>
          </p>
          <p className="text-sm sm:text-base text-gray-500 dark:text-white">Plan: {safeData.planName}</p>
        </div>

        <div className="grid gap-3">
          <div className="flex items-center justify-between">
            <p className="text-sm sm:text-base font-medium">Deductible</p>
            <p className="text-sm sm:text-base font-medium text-gray-500 dark:text-white">${safeData.deductible.total.toFixed(2)}</p>
          </div>
          <Progress value={deductibleProgress} className="w-full h-2 sm:h-3" />
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
            <p className="text-xs sm:text-sm text-gray-500 dark:text-white">
              Spent:<span className="text-xs sm:text-sm font-bold text-gray-900 dark:text-white"> ${safeData.deductible.spent.toFixed(2)}</span>
            </p>
            <p className="text-xs sm:text-sm text-gray-500 dark:text-white">
              Remaining:<span className="text-xs sm:text-sm font-bold text-gray-900 dark:text-white"> ${safeData.deductible.remaining.toFixed(2)}</span>
            </p>
          </div>
        </div>

        <div className="grid gap-3">
          <div className="flex items-center justify-between">
            <p className="text-sm sm:text-base font-medium">Out-of-Pocket Maximum</p>
            <p className="text-sm sm:text-base font-medium text-gray-500 dark:text-white">${safeData.outOfPocketMax.total.toFixed(2)}</p>
          </div>
          <Progress value={oopMaxProgress} className="w-full h-2 sm:h-3" />
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
            <p className="text-xs sm:text-sm text-gray-500 dark:text-white">
              Spent:<span className="text-xs sm:text-sm font-bold text-gray-900 dark:text-white"> ${safeData.outOfPocketMax.spent.toFixed(2)}</span>
            </p>
            <p className="text-xs sm:text-sm text-gray-500 dark:text-white">
              Remaining:<span className="text-xs sm:text-sm font-bold text-gray-900 dark:text-white"> ${safeData.outOfPocketMax.remaining.toFixed(2)}</span>
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PlanProgressCard;