import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Pill, User, Calendar, Repeat, Home, AlertCircle, Clock, FileText } from "lucide-react";
import { cn } from "@/lib/utils";

interface PrescriptionData {
  prescriptionId: string;
  memberId: string;
  memberName: string;
  medicationName: string;
  brandName?: string | null;
  dosage: string;
  frequency: string;
  route: string;
  notes?: string;
  lastFillDate?: string;
  daysSupply?: number;
  refillsRemaining?: number;
  isEnrolledInAutoRefill?: boolean;
  isEligibleForAdvancedHomeDelivery?: boolean;
  isEnrolledInAdvancedHomeDelivery?: boolean;
  tier?: string;
  priorAuthorization?: {
    status: string;
    expiryDate?: string;
  };
  conditionIds?: string[];
}

interface PrescriptionCardProps {
  data: PrescriptionData | PrescriptionData[];
}

export default function PrescriptionCard({ data }: PrescriptionCardProps) {
  // Handle both single prescription and array of prescriptions
  const prescriptions = Array.isArray(data) ? data : [data];

  if (!prescriptions || prescriptions.length === 0) {
    return <div className="text-destructive">No prescription data available.</div>;
  }

  return (
    <div className="grid gap-4">
      {prescriptions.map((prescription) => (
        <Card key={prescription.prescriptionId} className="shadow-md w-full max-w-none sm:max-w-md lg:max-w-lg xl:max-w-xl gap-0">
          <CardHeader className="p-4 sm:p-6 gap-0 border-b">
            <CardTitle className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
              <div className="flex items-center gap-2">
                <Pill size={24} className="text-white" />
                <span className="text-sm sm:text-base font-semibold">
                  {prescription.medicationName}
                  {prescription.brandName && (
                    <span className="text-muted-foreground font-normal">
                      {' '}({prescription.brandName})
                    </span>
                  )}
                </span>
              </div>
            </CardTitle>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <User size={14} />
              <span>{prescription.memberName}</span>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4 p-4 sm:p-6">
            {/* Dosage and Frequency */}
            <div className="space-y-2">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Dosage:</span>
                  <span className="text-sm">{prescription.dosage}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock size={14} />
                  <span className="text-sm">{prescription.frequency}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Route:</span>
                <span className="text-sm text-muted-foreground">{prescription.route}</span>
              </div>
            </div>

            {/* Badges for services */}
            <div className="flex flex-wrap gap-2">
              {prescription.tier && (
                <Badge variant="secondary" className="text-xs">
                  {prescription.tier}
                </Badge>
              )}
              {prescription.isEnrolledInAutoRefill && (
                <Badge variant="outline" className="flex items-center gap-1 text-xs">
                  <Repeat className="h-3 w-3" />
                  Auto-Refill
                </Badge>
              )}
              {prescription.isEnrolledInAdvancedHomeDelivery && (
                <Badge variant="outline" className="flex items-center gap-1 text-xs">
                  <Home className="h-3 w-3" />
                  Home Delivery
                </Badge>
              )}
            </div>

            {/* Last Fill Information */}
            {prescription.lastFillDate && (
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar size={14} />
                  <span className="font-medium">Last Fill:</span>
                  <span>{prescription.lastFillDate}</span>
                </div>
                {prescription.daysSupply && (
                  <span className="text-muted-foreground">
                    ({prescription.daysSupply} days supply)
                  </span>
                )}
              </div>
            )}

            {/* Refills Remaining */}
            {prescription.refillsRemaining !== undefined && (
              <div className="flex items-center gap-2 text-sm">
                <span className="font-medium">Refills Remaining:</span>
                <span className={cn(
                  prescription.refillsRemaining === 0 ? "text-orange-600" : "text-green-600"
                )}>
                  {prescription.refillsRemaining}
                </span>
              </div>
            )}

            {/* Prior Authorization */}
            {prescription.priorAuthorization && (
              <div className="flex items-start gap-2 text-sm p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
                <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div>
                  <span className="font-medium">Prior Authorization: </span>
                  <span className={cn(
                    prescription.priorAuthorization.status === 'Approved' ? "text-green-600" : 
                    prescription.priorAuthorization.status === 'Not Required' ? "text-gray-600" : "text-orange-600"
                  )}>
                    {prescription.priorAuthorization.status}
                  </span>
                  {prescription.priorAuthorization.expiryDate && (
                    <div className="text-muted-foreground text-xs mt-1">
                      Expires: {prescription.priorAuthorization.expiryDate}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Notes */}
            {prescription.notes && (
              <div className="flex items-start gap-2 text-sm p-3 bg-muted/50 rounded-lg">
                <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                <div>
                  <span className="font-medium">Notes: </span>
                  <span className="text-muted-foreground">{prescription.notes}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
