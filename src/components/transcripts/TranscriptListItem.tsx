'use client';

import { useState } from 'react';
import { Transcript } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface TranscriptListItemProps {
  transcript: Transcript;
  selectable?: boolean;
  selected?: boolean;
  onSelectionChange?: (selected: boolean, transcriptId: string) => void;
}

export function TranscriptListItem({
  transcript,
  selectable = false,
  selected = false,
  onSelectionChange
}: TranscriptListItemProps) {
  const [isOpen, setIsOpen] = useState(false);

  const formattedDate = new Date(transcript.startedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const formattedTime = new Date(transcript.startedAt).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const handleSelectionChange = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked, transcript.id);
    }
  };

  return (
    <Card className={selectable && selected ? "ring-2 ring-blue-500 rounded-xs" : "rounded-xs"}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CardHeader className="mb-4">
          <CardTitle>
            <div className="flex flex-row items-center gap-2">
              {selectable && (
                <Checkbox
                  id={`transcript-${transcript.id}`}
                  checked={selected}
                  onCheckedChange={handleSelectionChange}
                  className="mr-2"
                />
              )}
              <span className="text-sm text-zinc-800 dark:text-zinc-100">{formattedDate}</span>
              <span className="text-sm text-zinc-800 dark:text-zinc-100">at</span>
              <span className="text-sm text-zinc-800 dark:text-zinc-100">{formattedTime}</span>
            </div>
          </CardTitle>
          <CardDescription className="text-base text-zinc-800 dark:text-zinc-100">
            {transcript.summary}
          </CardDescription>
        </CardHeader>
        <CardContent >
            <CollapsibleTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center">
                    {isOpen ? 'Hide' : 'Read Full'} Transcript
                    {isOpen ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
                </Button>
            </CollapsibleTrigger>
        </CardContent>
        <CollapsibleContent>
            <div className="p-6 pt-0 mt-4">
                <ScrollArea className="h-48 w-full rounded-md border p-4 dark:bg-zinc-800">
                    <p className="text-sm whitespace-pre-wrap">{transcript.transcript}</p>
                </ScrollArea>
                <div className="mt-4">
                    <audio controls src={transcript.recordingUrls.stereo} className="flex w-full bg-white h-20 items-center justify-top p-4">
                        Your browser does not support the audio element.
                    </audio>
                </div>
            </div>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
} 