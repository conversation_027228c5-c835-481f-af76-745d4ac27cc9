import { Transcript } from '@/types';
import { TranscriptListItem } from './TranscriptListItem';

interface TranscriptListProps {
  transcripts: Transcript[];
}

export function TranscriptList({ transcripts }: TranscriptListProps) {
  if (transcripts.length === 0) {
    return <p className="text-muted-foreground">No transcripts found.</p>;
  }

  return (
    <div className="space-y-4">
      {transcripts.map((transcript) => (
        <TranscriptListItem key={transcript.id} transcript={transcript} />
      ))}
    </div>
  );
} 