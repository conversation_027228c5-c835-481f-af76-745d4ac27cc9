'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Pill, Repeat, Home, AlertCircle, User } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface Prescription {
  prescriptionId: string;
  memberId: string;
  memberName: string;
  medicationName: string;
  brandName?: string | null;
  dosage: string;
  frequency: string;
  route: string;
  notes?: string;
  lastFillDate?: string;
  daysSupply?: number;
  refillsRemaining?: number;
  isEnrolledInAutoRefill?: boolean;
  isEligibleForHomeDelivery?: boolean;
  isEnrolledInHomeDelivery?: boolean;
  tier?: string;
  priorAuthorization?: {
    status: string;
    expiryDate?: string;
  };
}

interface PrescriptionsCardProps {
  prescriptions: Prescription[];
}

export function PrescriptionsCard({ prescriptions }: PrescriptionsCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Pill className="h-5 w-5" />
          Prescriptions & Supplements
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {prescriptions.map((prescription) => (
            <div
              key={prescription.prescriptionId}
              className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 space-y-3"
            >
              <div className="flex items-start justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-500">{prescription.memberName}</span>
                  </div>
                  <h3 className="font-medium">
                    {prescription.medicationName}
                    {prescription.brandName && (
                      <span className="text-gray-500 dark:text-gray-400">
                        {' '}
                        ({prescription.brandName})
                      </span>
                    )}
                  </h3>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {prescription.dosage} - {prescription.frequency}
                  </div>
                </div>
                <div className="flex gap-2">
                  {prescription.isEnrolledInAutoRefill && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Repeat className="h-3 w-3" />
                      Auto-Refill
                    </Badge>
                  )}
                  {prescription.isEnrolledInHomeDelivery && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Home className="h-3 w-3" />
                      Home Delivery
                    </Badge>
                  )}
                </div>
              </div>

              {prescription.lastFillDate && (
                <div className="text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Last Fill:</span>{' '}
                  {prescription.lastFillDate}
                  {prescription.daysSupply && ` (${prescription.daysSupply} days supply)`}
                  {prescription.refillsRemaining !== undefined && (
                    <span className="ml-2">
                      - {prescription.refillsRemaining} refills remaining
                    </span>
                  )}
                </div>
              )}

              {prescription.tier && (
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Tier:</span>
                  <Badge variant="secondary">{prescription.tier}</Badge>
                </div>
              )}

              {prescription.priorAuthorization && (
                <div className="flex items-center gap-2 text-sm">
                  <AlertCircle className="h-4 w-4 text-yellow-500" />
                  <span>
                    Prior Authorization: {prescription.priorAuthorization.status}
                    {prescription.priorAuthorization.expiryDate && 
                      ` (Expires: ${prescription.priorAuthorization.expiryDate})`}
                  </span>
                </div>
              )}

              {prescription.notes && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {prescription.notes}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}