import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ProfileData } from '@/types';
import { User, Calendar, MapPin, Heart, Shield } from 'lucide-react';

interface ProfileCardProps {
  profile: ProfileData;
}

export function ProfileCard({ profile }: ProfileCardProps) {
  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center space-x-4">
        <Avatar className="h-20 w-20">
          <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${profile.memberName}`} alt={profile.memberName} />
          <AvatarFallback>{profile.memberName.split(' ').map(n => n[0]).join('')}</AvatarFallback>
        </Avatar>
        <div>
          <CardTitle className="text-2xl">{profile.memberName}</CardTitle>
          <p className="text-sm text-muted-foreground">{profile.memberId}</p>
          <Badge variant="secondary" className="mt-1">{profile.gender}</Badge>
        </div>
      </CardHeader>
      <CardContent className="grid gap-4">
        <div className="flex items-center">
          <User className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{profile.familyStatus}</span>
        </div>
        <div className="flex items-center">
          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="text-sm">Born: {profile.dateOfBirth}</span>
        </div>
        <div className="flex items-center">
          <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{profile.location}</span>
        </div>
        <div className="flex items-center">
          <Shield className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {profile.insurancePlan.planName} ({profile.insurancePlan.planType})
          </span>
        </div>
      </CardContent>
    </Card>
  );
}