import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Claim } from '@/types';
import { DollarSign } from 'lucide-react';

interface ClaimsCardProps {
  claims: Claim[];
}

export function ClaimsCard({ claims }: ClaimsCardProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Claims History</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Provider</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Patient Responsibility</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {claims.map((claim) => (
              <TableRow key={claim.claimId}>
                <TableCell>{claim.claimDate}</TableCell>
                <TableCell>{claim.providerName}</TableCell>
                <TableCell>{claim.procedureDescription}</TableCell>
                <TableCell>
                  <Badge variant={claim.claimStatus === 'Paid' ? 'default' : 'secondary'}>
                    {claim.claimStatus}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">${claim.patientResponsibility.toFixed(2)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}