import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PlanUsage } from '@/types';
import { DollarSign, TrendingUp } from 'lucide-react';

interface PlanUsageCardProps {
  planUsage: PlanUsage;
}

export function PlanUsageCard({ planUsage }: PlanUsageCardProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-base sm:text-lg">Plan Usage ({planUsage.year})</CardTitle>
        <p className="text-xs sm:text-sm text-muted-foreground">As of {planUsage.asOf}</p>
      </CardHeader>
      <CardContent className="grid gap-4 sm:gap-6">
        <div>
          <h3 className="text-base sm:text-lg font-semibold mb-2">Deductible</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <span className="text-xs sm:text-sm font-medium">Individual</span>
                <span className="text-xs sm:text-sm text-muted-foreground">
                  ${planUsage.deductible.individual.met.toFixed(2)} / ${planUsage.deductible.individual.amount.toFixed(2)}
                </span>
              </div>
              <Progress value={planUsage.deductible.individual.percentMet} className="w-full" />
              <p className="text-xs text-muted-foreground">{planUsage.deductible.individual.percentMet.toFixed(1)}% met</p>
            </div>
            <div className="space-y-2">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <span className="text-xs sm:text-sm font-medium">Family</span>
                <span className="text-xs sm:text-sm text-muted-foreground">
                  ${planUsage.deductible.family.met.toFixed(2)} / ${planUsage.deductible.family.amount.toFixed(2)}
                </span>
              </div>
              <Progress value={planUsage.deductible.family.percentMet} className="w-full" />
              <p className="text-xs text-muted-foreground">{planUsage.deductible.family.percentMet.toFixed(1)}% met</p>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-base sm:text-lg font-semibold mb-2">Out-of-Pocket Max</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <span className="text-xs sm:text-sm font-medium">Individual</span>
                <span className="text-xs sm:text-sm text-muted-foreground">
                  ${planUsage.outOfPocketMax.individual.met.toFixed(2)} / ${planUsage.outOfPocketMax.individual.amount.toFixed(2)}
                </span>
              </div>
              <Progress value={planUsage.outOfPocketMax.individual.percentMet} className="w-full" />
              <p className="text-xs text-muted-foreground">{planUsage.outOfPocketMax.individual.percentMet.toFixed(1)}% met</p>
            </div>
            <div className="space-y-2">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <span className="text-xs sm:text-sm font-medium">Family</span>
                <span className="text-xs sm:text-sm text-muted-foreground">
                  ${planUsage.outOfPocketMax.family.met.toFixed(2)} / ${planUsage.outOfPocketMax.family.amount.toFixed(2)}
                </span>
              </div>
              <Progress value={planUsage.outOfPocketMax.family.percentMet} className="w-full" />
              <p className="text-xs text-muted-foreground">{planUsage.outOfPocketMax.family.percentMet.toFixed(1)}% met</p>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-base sm:text-lg font-semibold mb-2">Benefit Utilization</h3>

          {/* Mobile view - Card layout */}
          <div className="block sm:hidden space-y-3">
            <div className="border rounded-lg p-3">
              <h4 className="font-medium text-sm mb-2">Specialist Visits</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>Used: {planUsage.benefitUtilization.specialistVisits.used}</div>
                <div>Covered: {planUsage.benefitUtilization.specialistVisits.covered}</div>
                <div className="col-span-2">Copay: ${planUsage.benefitUtilization.specialistVisits.copay?.toFixed(2)}</div>
              </div>
            </div>

            <div className="border rounded-lg p-3">
              <h4 className="font-medium text-sm mb-2">Primary Care Visits</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>Used: {planUsage.benefitUtilization.primaryCareVisits.used}</div>
                <div>Covered: {planUsage.benefitUtilization.primaryCareVisits.covered}</div>
                <div className="col-span-2">Copay: ${planUsage.benefitUtilization.primaryCareVisits.copay?.toFixed(2)}</div>
              </div>
            </div>

            <div className="border rounded-lg p-3">
              <h4 className="font-medium text-sm mb-2">Prescription Drugs (Tier 1)</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>Used: {planUsage.benefitUtilization.prescriptionDrugs.tier1.used}</div>
                <div>Covered: {planUsage.benefitUtilization.prescriptionDrugs.tier1.covered}</div>
                <div className="col-span-2">Copay: ${planUsage.benefitUtilization.prescriptionDrugs.tier1.copay?.toFixed(2)}</div>
              </div>
            </div>

            <div className="border rounded-lg p-3">
              <h4 className="font-medium text-sm mb-2">Prescription Drugs (Tier 2)</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>Used: {planUsage.benefitUtilization.prescriptionDrugs.tier2.used}</div>
                <div>Covered: {planUsage.benefitUtilization.prescriptionDrugs.tier2.covered}</div>
                <div className="col-span-2">Copay: ${planUsage.benefitUtilization.prescriptionDrugs.tier2.copay?.toFixed(2)}</div>
              </div>
            </div>

            <div className="border rounded-lg p-3">
              <h4 className="font-medium text-sm mb-2">Specialty Drugs</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>Used: {planUsage.benefitUtilization.prescriptionDrugs.specialtyDrugs.used}</div>
                <div>Covered: {planUsage.benefitUtilization.prescriptionDrugs.specialtyDrugs.covered}</div>
                <div className="col-span-2">Coinsurance: {planUsage.benefitUtilization.prescriptionDrugs.specialtyDrugs.coinsurance}%</div>
              </div>
            </div>

            <div className="border rounded-lg p-3">
              <h4 className="font-medium text-sm mb-2">Preventive Care</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>Used: {planUsage.benefitUtilization.preventiveCare.used}</div>
                <div>Covered: {planUsage.benefitUtilization.preventiveCare.covered}</div>
                <div className="col-span-2">Copay: ${planUsage.benefitUtilization.preventiveCare.copay?.toFixed(2)}</div>
              </div>
            </div>

            <div className="border rounded-lg p-3">
              <h4 className="font-medium text-sm mb-2">Mental Health Visits</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>Used: {planUsage.benefitUtilization.mentalHealthVisits.used}</div>
                <div>Covered: {planUsage.benefitUtilization.mentalHealthVisits.covered}</div>
                <div>Remaining: {planUsage.benefitUtilization.mentalHealthVisits.remaining}</div>
                <div>Copay: ${planUsage.benefitUtilization.mentalHealthVisits.copay?.toFixed(2)}</div>
              </div>
            </div>
          </div>

          {/* Desktop view - Table layout */}
          <div className="hidden sm:block overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Benefit</TableHead>
                  <TableHead>Used</TableHead>
                  <TableHead>Covered</TableHead>
                  <TableHead>Copay/Coinsurance</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>Specialist Visits</TableCell>
                  <TableCell>{planUsage.benefitUtilization.specialistVisits.used}</TableCell>
                  <TableCell>{planUsage.benefitUtilization.specialistVisits.covered}</TableCell>
                  <TableCell>${planUsage.benefitUtilization.specialistVisits.copay?.toFixed(2)}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Primary Care Visits</TableCell>
                  <TableCell>{planUsage.benefitUtilization.primaryCareVisits.used}</TableCell>
                  <TableCell>{planUsage.benefitUtilization.primaryCareVisits.covered}</TableCell>
                  <TableCell>${planUsage.benefitUtilization.primaryCareVisits.copay?.toFixed(2)}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Prescription Drugs (Tier 1)</TableCell>
                  <TableCell>{planUsage.benefitUtilization.prescriptionDrugs.tier1.used}</TableCell>
                  <TableCell>{planUsage.benefitUtilization.prescriptionDrugs.tier1.covered}</TableCell>
                  <TableCell>${planUsage.benefitUtilization.prescriptionDrugs.tier1.copay?.toFixed(2)}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Prescription Drugs (Tier 2)</TableCell>
                  <TableCell>{planUsage.benefitUtilization.prescriptionDrugs.tier2.used}</TableCell>
                  <TableCell>{planUsage.benefitUtilization.prescriptionDrugs.tier2.covered}</TableCell>
                  <TableCell>${planUsage.benefitUtilization.prescriptionDrugs.tier2.copay?.toFixed(2)}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Specialty Drugs</TableCell>
                  <TableCell>{planUsage.benefitUtilization.prescriptionDrugs.specialtyDrugs.used}</TableCell>
                  <TableCell>{planUsage.benefitUtilization.prescriptionDrugs.specialtyDrugs.covered}</TableCell>
                  <TableCell>{planUsage.benefitUtilization.prescriptionDrugs.specialtyDrugs.coinsurance}%</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Preventive Care</TableCell>
                  <TableCell>{planUsage.benefitUtilization.preventiveCare.used}</TableCell>
                  <TableCell>{planUsage.benefitUtilization.preventiveCare.covered}</TableCell>
                  <TableCell>${planUsage.benefitUtilization.preventiveCare.copay?.toFixed(2)}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Mental Health Visits</TableCell>
                  <TableCell>{planUsage.benefitUtilization.mentalHealthVisits.used}</TableCell>
                  <TableCell>{planUsage.benefitUtilization.mentalHealthVisits.covered} (Remaining: {planUsage.benefitUtilization.mentalHealthVisits.remaining})</TableCell>
                  <TableCell>${planUsage.benefitUtilization.mentalHealthVisits.copay?.toFixed(2)}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>

        <div>
          <h3 className="text-base sm:text-lg font-semibold mb-2">Spending by Category</h3>
          <p className="text-xs sm:text-sm text-muted-foreground mb-4">Total Spent: ${planUsage.spendingByCategory.totalSpent.toFixed(2)}</p>

          {/* Mobile view - Card layout */}
          <div className="block sm:hidden space-y-3">
            {planUsage.spendingByCategory.byCategory.map((item) => (
              <div key={item.category} className="border rounded-lg p-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-sm">{item.category}</h4>
                  <div className="text-right">
                    <div className="text-sm font-medium">${item.amount.toFixed(2)}</div>
                    <div className="text-xs text-muted-foreground">{item.percentOfTotal}%</div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Desktop view - Table layout */}
          <div className="hidden sm:block overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="text-right">Percent of Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {planUsage.spendingByCategory.byCategory.map((item) => (
                  <TableRow key={item.category}>
                    <TableCell>{item.category}</TableCell>
                    <TableCell className="text-right">${item.amount.toFixed(2)}</TableCell>
                    <TableCell className="text-right">{item.percentOfTotal}%</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        <div>
          <h3 className="text-base sm:text-lg font-semibold mb-2">Recent Activity</h3>

          {/* Mobile view - Card layout */}
          <div className="block sm:hidden space-y-3">
            {planUsage.recentActivity.map((activity, index) => (
              <div key={index} className="border rounded-lg p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="text-xs text-muted-foreground">{activity.date}</div>
                  <div className="text-sm font-medium">${activity.amount.toFixed(2)}</div>
                </div>
                <div className="text-sm font-medium mb-1">{activity.type}</div>
                <div className="text-xs text-muted-foreground">{activity.description}</div>
              </div>
            ))}
          </div>

          {/* Desktop view - Table layout */}
          <div className="hidden sm:block overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {planUsage.recentActivity.map((activity, index) => (
                  <TableRow key={index}>
                    <TableCell>{activity.date}</TableCell>
                    <TableCell>{activity.type}</TableCell>
                    <TableCell>{activity.description}</TableCell>
                    <TableCell className="text-right">${activity.amount.toFixed(2)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}