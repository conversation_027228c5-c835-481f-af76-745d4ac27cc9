import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { MedicalCondition } from '@/types';
import { ClipboardList } from 'lucide-react';

interface MedicalConditionsCardProps {
  conditions: MedicalCondition[];
}

export function MedicalConditionsCard({ conditions }: MedicalConditionsCardProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Medical Conditions</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Condition</TableHead>
              <TableHead>Diagnosis Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Notes</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {conditions.map((condition) => (
              <TableRow key={condition.conditionId}>
                <TableCell className="font-medium">
                  {condition.conditionName} {condition.isPrimary && <Badge variant="secondary">Primary</Badge>}
                </TableCell>
                <TableCell>{condition.diagnosisDate}</TableCell>
                <TableCell>
                  <Badge variant={condition.status === 'Active' ? 'destructive' : 'default'}>
                    {condition.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">{condition.notes}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}