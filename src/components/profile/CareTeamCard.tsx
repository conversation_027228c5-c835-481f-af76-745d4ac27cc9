import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { CareTeamMember } from '@/types';
import { Stethoscope, Phone, MapPin } from 'lucide-react';

interface CareTeamCardProps {
  careTeam: CareTeamMember[];
}

export function CareTeamCard({ careTeam }: CareTeamCardProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Care Team</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Specialty</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Contact</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {careTeam.map((member) => (
              <TableRow key={member.providerId}>
                <TableCell className="font-medium">
                  {member.name} {member.isPrimary && <Badge variant="outline">PCP</Badge>}
                </TableCell>
                <TableCell>{member.specialty}</TableCell>
                <TableCell>{member.role}</TableCell>
                <TableCell>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Phone className="mr-1 h-3 w-3" /> {member.phone}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="mr-1 h-3 w-3" /> {member.location}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}