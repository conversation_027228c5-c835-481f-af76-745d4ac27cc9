import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { HealthRecord } from '@/types';
import { FileText } from 'lucide-react';

interface HealthRecordsCardProps {
  healthRecords: HealthRecord[];
}

export function HealthRecordsCard({ healthRecords }: HealthRecordsCardProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Health Records</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Provider</TableHead>
              <TableHead>Description</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {healthRecords.map((record) => (
              <TableRow key={record.recordId}>
                <TableCell>{record.recordDate}</TableCell>
                <TableCell>{record.recordType}</TableCell>
                <TableCell>{record.providerName}</TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {record.description || record.summary || record.resultsSummary}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}