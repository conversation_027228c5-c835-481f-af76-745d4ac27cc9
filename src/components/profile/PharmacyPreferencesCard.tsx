'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Store, Truck } from 'lucide-react';

interface PharmacyPreferences {
  defaultRetailPharmacy: {
    name: string;
    address: string;
  };
  homeDelivery: {
    address: string;
  };
}

interface PharmacyPreferencesCardProps {
  pharmacyPreferences: PharmacyPreferences;
}

export function PharmacyPreferencesCard({ pharmacyPreferences }: PharmacyPreferencesCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Store className="h-5 w-5" />
          Pharmacy Preferences
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Default Retail Pharmacy</h3>
            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
              <div className="font-medium">{pharmacyPreferences.defaultRetailPharmacy.name}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {pharmacyPreferences.defaultRetailPharmacy.address}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center gap-2">
              <Truck className="h-4 w-4" />
              Home Delivery Address
            </h3>
            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {pharmacyPreferences.homeDelivery.address}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 