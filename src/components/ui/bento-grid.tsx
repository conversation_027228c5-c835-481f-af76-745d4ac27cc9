import { cn } from "@/lib/utils";
import React from "react";
import { useRouter } from 'next/navigation';
import { useChat } from "@/contexts/ChatContext";

export const BentoGrid = ({
  className,
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        "mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-3",
        className,
      )}
    >
      {children}
    </div>
  );
};

export const BentoGridItem = ({
  className,
  title,
  description,
  header,
  icon,
  href,
}: {
  className?: string;
  title?: string | React.ReactNode;
  description?: string | React.ReactNode;
  header?: React.ReactNode;
  icon?: React.ReactNode;
  href?: string;
}) => {
  const router = useRouter();
  const { createThread, threads } = useChat();

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    
    if (href?.startsWith('/?prompt=')) {
      const prompt = decodeURIComponent(href.replace('/?prompt=', ''));
      createThread({
        name: `New Chat ${threads.length + 1}`,
        initialMessage: prompt
      });
      router.push('/journeys');
    } else if (href) {
      router.push(href);
    }
  };

  const content = (
    <div
      className={cn(
        "group/bento h-[16rem] shadow-input row-span-1 flex flex-col space-y-4 rounded-xl border border-neutral-200 bg-white p-4 transition duration-200 hover:shadow-xl dark:border-white/[0.2] dark:bg-black dark:shadow-none bento-grid-item",
        className,
      )}
    >
      <div className="relative aspect-[4/3] w-full h-[100px]">
        {header}
      </div>
      <div className="transition duration-200 group-hover/bento:translate-x-2">
        {icon}
        <div className="mt-2 mb-2 font-sans font-bold text-neutral-600 dark:text-[#38D7FF]">
          {title}
        </div>
        <div className="font-sans text-xs font-normal text-neutral-600 dark:text-neutral-300">
          {description}
        </div>
      </div>
    </div>
  );

  if (href) {
    return (
      <a 
        href={href} 
        className={cn("block", className)}
        onClick={handleClick}
      >
        {content}
      </a>
    );
  }

  return content;
}; 