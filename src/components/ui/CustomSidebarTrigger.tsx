"use client";

import { SidebarTrigger, useSidebar } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";

export function CustomSidebarTrigger() {
  const { open } = useSidebar();
  const isMobile = useIsMobile();

  const sidebarWidth = 'var(--sidebar-width, 280px)';
  // Only offset on desktop
  const left = !isMobile && open ? `calc(${sidebarWidth} - 40px)` : '10px';

  return (
    <SidebarTrigger
      className="fixed top-3.5 z-50"
      style={{ left }}
    />
  );
} 