"use client";
import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import type { ReactNode } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

interface FadeInOnScrollProps {
  children: ReactNode;
  className?: string;
}

export function FadeInOnScroll({ children, className }: FadeInOnScrollProps) {
  const ref = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const margin = isMobile ? "-100px" : "-200px";
  const isInView = useInView(ref, { once: true, margin });

  // Only render the section when in view
  return (
    <div ref={ref} className={className}>
      {!isInView && (
        <div className="fixed bottom-2 flex flex-row items-center gap-2 cursor-pointer hover:text-zinc-800 dark:hover:text-zinc-200 transition-colors"> 
          Explore More<span className="font-semibold"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M4 13L8 9L12 13" stroke="#ffffff" strokeWidth="0.886667" strokeLinecap="round" strokeLinejoin="round"></path><path d="M4 9L8 5L12 9" stroke="#ffffff" strokeWidth="0.886667" strokeLinecap="round" strokeLinejoin="round"></path></svg></span>
        </div>
      )}
      {isInView && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, ease: "easeOut" }}
        >
          {children}
        </motion.div>
      )}
    </div>
  );
} 