
import { promises as fs } from 'fs';
import path from 'path';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProfileData } from '@/types';
import { ProfileCard } from '@/components/profile/ProfileCard';
import { CareTeamCard } from '@/components/profile/CareTeamCard';
import { MedicalConditionsCard } from '@/components/profile/MedicalConditionsCard';
import { PrescriptionsCard } from '@/components/profile/PrescriptionsCard';
import { HealthRecordsCard } from '@/components/profile/HealthRecordsCard';
import { ClaimsCard } from '@/components/profile/ClaimsCard';
import { PlanUsageCard } from '@/components/profile/PlanUsageCard';
import { PaymentMethodsCard } from '@/components/profile/PaymentMethodsCard';
import { PharmacyPreferencesCard } from '@/components/profile/PharmacyPreferencesCard';
import { CareCanvasDemo } from '@/components/care-canvas/CareCanvasDemo';


export default async function ProfilePage() {
  const filePath = path.join(process.cwd(), 'src/app/data/profile.json');
  const fileContents = await fs.readFile(filePath, 'utf8');
  const data: { profile: ProfileData } = JSON.parse(fileContents);
  const profile = data.profile;

  return (
    <div className="container mx-auto py-4 px-4 md:px-6 lg:px-8 dark:bg-[#262a3f]">
      {/* <div className="mb-6 flex flex-row items-start justify-start">
        <Link href="/journeys" passHref>
          <Button variant="outline" className="flex items-center gap-2 bg-transparent">
            <ArrowLeft className="h-4 w-4" />
            Back to Chat
          </Button>
        </Link>
      </div> */}
      <h1 className="text-4xl font-bold mb-8 text-center text-gray-900 dark:text-gray-100 leading-relaxed">My Health Profile</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-1">
          <ProfileCard profile={profile} />
        </div>
        <div className="lg:col-span-2 grid grid-cols-1 gap-6">
          <CareTeamCard careTeam={profile.careTeam} />
          {/* <MedicalConditionsCard conditions={profile.medicalConditions} /> */}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <PrescriptionsCard prescriptions={profile.prescriptions} />
        <div className="grid grid-cols-1 gap-6">
          <PharmacyPreferencesCard pharmacyPreferences={profile.pharmacyPreferences} />
          <PaymentMethodsCard paymentMethods={profile.paymentMethods} />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <HealthRecordsCard healthRecords={profile.healthRecords} />
        <ClaimsCard claims={profile.claims} />
      </div>

      <div id="plan-usage" className="grid grid-cols-1 gap-6">
        <PlanUsageCard planUsage={profile.planUsage} />
      </div>

      {/* Care Canvas Demo Section */}
      <CareCanvasDemo />
    </div>
  );
}