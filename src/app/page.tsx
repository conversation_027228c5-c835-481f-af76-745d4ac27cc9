'use client';

import { WelcomeTextArea } from "@/components/chat/WelcomeTextArea";
import { Button } from "@/components/ui/button";
import { RevealSection } from "@/components/scroll-reveal/reveal";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FadeInOnScroll } from "@/components/animate-ui/FadeInOnScroll";
import { Bento } from "@/components/home-bento-grid/bento";

export default function HomePage() {
  const [showReveal, setShowReveal] = useState(false);

  // Simplified layout inspired by the image
  return (
    <main
      className="flex flex-col items-center justify-center text-foreground p-4 "
      role="application"
      aria-label="Virtual Concierge"
    >
      <div className="w-full max-w-2xl flex flex-col items-center gap-8 h-full mt-[calc(10vh)]">
        <h1 id="VC_h1" className="text-center text-zinc-800 leading-relaxed">
          Virtual Concierge
        </h1>

        <h2 className="text-center text-zinc-800">
          Your personalized healthcare journey starts here.
        </h2>

        <WelcomeTextArea placeholder="Ask me anything..." />


        {/* <div className="flex flex-wrap justify-center gap-3">
          <Button variant="outline" className="bg-white dark:bg-zinc-800 hover:bg-zinc-100 dark:hover:bg-zinc-700 border-zinc-300 dark:border-zinc-700 text-zinc-700 dark:text-zinc-300">
            Button 1
          </Button>
          <Button variant="outline" className="bg-white dark:bg-zinc-800 hover:bg-zinc-100 dark:hover:bg-zinc-700 border-zinc-300 dark:border-zinc-700 text-zinc-700 dark:text-zinc-300">
            Button 2
          </Button>
          <Button variant="outline" className="bg-white dark:bg-zinc-800 hover:bg-zinc-100 dark:hover:bg-zinc-700 border-zinc-300 dark:border-zinc-700 text-zinc-700 dark:text-zinc-300">
            Button 3
          </Button>
          <Button variant="outline" className="bg-white dark:bg-zinc-800 hover:bg-zinc-100 dark:hover:bg-zinc-700 border-zinc-300 dark:border-zinc-700 text-zinc-700 dark:text-zinc-300">
            Button 4
          </Button>
        </div> */}

      </div>
      {/* <AnimatePresence>
        {showReveal && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <RevealSection />
          </motion.div>
        )}
      </AnimatePresence>
      <AnimatePresence>
        {!showReveal && (
          <motion.div 
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            onClick={() => setShowReveal(true)}
            className="absolute bottom-5 text-sm text-zinc-600 dark:text-zinc-400 flex flex-row items-center gap-2 cursor-pointer hover:text-zinc-800 dark:hover:text-zinc-200 transition-colors"
          >
            Explore For You <span className="font-semibold"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M4 13L8 9L12 13" stroke="#858481" strokeWidth="0.886667" strokeLinecap="round" strokeLinejoin="round"></path><path d="M4 9L8 5L12 9" stroke="#B9B9B7" strokeWidth="0.886667" strokeLinecap="round" strokeLinejoin="round"></path></svg></span>
          </motion.div>
        )}
      </AnimatePresence> */}

      {/* <FadeInOnScroll className="w-full max-w-3xl flex flex-col items-center gap-8 min-h-200 mt-20 mb-10 ">
        <div className=" space-y-8">
          <h3 className="text-xl font-semibold tracking-tight text-center">
            Explore to your personalized care programs, prescriptions, and benefits.
          </h3><Bento />
        </div>
       

      </FadeInOnScroll> */}
    </main>
  );
}
