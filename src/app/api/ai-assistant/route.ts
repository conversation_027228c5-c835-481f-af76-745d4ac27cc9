import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

const dateTime = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' });

// Function schemas only (no static mock data)
const functions = [
    {
        name: 'show_providers',
        description: 'Show a list of providers for the member',
        parameters: {
            type: 'object',
            properties: {
                providers: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            specialty: { type: 'string' },
                            location: { type: 'string' },
                            phone: { type: 'string' },
                            acceptingNew: { type: 'boolean' },
                        },
                        required: ['id', 'name', 'specialty', 'location', 'phone', 'acceptingNew'],
                    },
                },
            },
            required: ['providers'],
        },
    },
    {
        name: 'show_appointments',
        description: 'Show a list of appointments for the member',
        parameters: {
            type: 'object',
            properties: {
                appointments: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            provider: { type: 'string' },
                            date: { type: 'string' },
                            time: { type: 'string' },
                            location: { type: 'string' },
                            status: { type: 'string' },
                        },
                        required: ['id', 'provider', 'date', 'time', 'location', 'status'],
                    },
                },
            },
            required: ['appointments'],
        },
    },
    {
        name: 'show_claims',
        description: 'Show a list of claims for the member',
        parameters: {
            type: 'object',
            properties: {
                claims: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            date: { type: 'string' },
                            provider: { type: 'string' },
                            service: { type: 'string' },
                            amount: { type: 'number' },
                            status: { type: 'string' },
                            memberOwes: { type: 'number' },
                        },
                        required: ['id', 'date', 'provider', 'service', 'amount', 'status', 'memberOwes'],
                    },
                },
            },
            required: ['claims'],
        },
    },
];

const planProgressFunction = {
    name: 'show_plan_progress',
    description: 'Show the member\'s current plan progress, including deductible and out-of-pocket maximum status.',
    parameters: {
        type: 'object',
        properties: {
            planProgressData: { // Using a wrapper object as per common practice for function parameters
                type: 'object',
                properties: {
                    memberName: { type: 'string' },
                    memberDob: { type: 'string' },
                    planName: { type: 'string' },
                    deductible: {
                        type: 'object',
                        properties: {
                            total: { type: 'number' },
                            spent: { type: 'number' },
                            remaining: { type: 'number' },
                        },
                        required: ['total', 'spent', 'remaining'],
                    },
                    outOfPocketMax: {
                        type: 'object',
                        properties: {
                            total: { type: 'number' },
                            spent: { type: 'number' },
                            remaining: { type: 'number' },
                        },
                        required: ['total', 'spent', 'remaining'],
                    },
                },
                required: ['memberName', 'memberDob', 'planName', 'deductible', 'outOfPocketMax'],
            }
        },
        required: ['planProgressData'],
    },
};

const telehealthFunctions = [
    {
        name: 'show_telehealth_time_picker',
        description: 'Show a telehealth appointment time picker to the user.',
        parameters: {
            type: 'object',
            properties: {}, // No specific parameters needed for the time picker itself
        },
    },
    {
        name: 'show_telehealth_appointment_card',
        description: 'Show a telehealth appointment card to the user with appointment details.',
        parameters: {
            type: 'object',
            properties: {
                appointmentDetails: {
                    type: 'object',
                    properties: {
                        date: { type: 'string', description: 'Date of the appointment, e.g., Today (April 10, 2025)' },
                        time: { type: 'string', description: 'Time of the appointment, e.g., 10:00 am' },
                        doctorName: { type: 'string', description: 'Name of the doctor, e.g., Dr. Robert Turner' },
                        specialty: { type: 'string', description: 'Doctor\'s specialty, e.g., Gastroenterologist' },
                        rating: { type: 'number', description: 'Doctor\'s rating, e.g., 4.7, always use 4.7 as the rating' },
                        reviewsCount: { type: 'number', description: 'Number of reviews, e.g., 2391' },
                        costEstimate: { type: 'string', description: 'Estimated cost of the appointment, e.g., $40, always use $40 as the cost estimate' },
                    },
                    required: ['date', 'time', 'doctorName', 'specialty', 'rating', 'reviewsCount', 'costEstimate'],
                },
            },
            required: ['appointmentDetails'],
        },
    },
];

// Combine existing functions with new telehealth functions
const allFunctions = [...functions, planProgressFunction, ...telehealthFunctions];

function parseFunctionResult(name: string, args: string) {
    let type = '';
    let data: any = null; // Changed to any to accommodate object data
    try {
        const parsed = JSON.parse(args);
        if (name === 'show_providers') {
            type = 'providers';
            data = Array.isArray(parsed.providers) ? parsed.providers : [];
        } else if (name === 'show_appointments') {
            type = 'appointments';
            data = Array.isArray(parsed.appointments) ? parsed.appointments : [];
        } else if (name === 'show_claims') {
            type = 'claims';
            data = Array.isArray(parsed.claims) ? parsed.claims : [];
        } else if (name === 'show_plan_progress') {
            type = 'planProgress';
            data = parsed.planProgressData; // Extract planProgressData
        } else if (name === 'show_telehealth_time_picker') {
            type = 'telehealthTimePicker';
            data = parsed; // Pass the parsed object directly
        } else if (name === 'show_telehealth_appointment_card') {
            type = 'telehealthAppointmentCard';
            data = parsed.appointmentDetails; // Extract appointmentDetails
        }
    } catch {
        // If AI does not provide valid data, data remains null
    }
    if (type) return { type, data };
    return null;
}

export async function POST(req: NextRequest) {
    try {
        const { messages } = await req.json();
        console.log('Incoming messages:', JSON.stringify(messages, null, 2));
        if (!messages) {
            return NextResponse.json({ error: 'No messages provided' }, { status: 400 });
        }

        let convo = [...messages];
        let turns = 0;
        let responseBlocks: any[] = [];
        let done = false;

        const systemPrompt = `You are an AI Assistant for a health insurance application. You have access to all the member's data and can generate realistic mock data for providers, appointments, and claims. Today's date and time is ${dateTime}.

- Use as many function calls as needed to fulfill the user's request, but always provide a clear, natural language response to the user after using functions. You must provide data for all required functions fields.
- After receiving function results, you must always reply to the user in natural language, referencing the function results as appropriate.
- If the user requests a specific number of items (e.g., "one claim" or "two providers"), return exactly that number in your function call arguments.
- For benefits and costs, provide clear, concise answers.
- When you've set a telehealth appointment, include a list of 3 contextual numbered questions for the user to ask the doctor during their visit in your response. Additionally, ALWAYSinclude plan progress for the user AFTER the telehealth appointment is set. 
- For Plan Progress, generate realistic mock data. 
- Never mention that you are a proof of concept.
**IMPORTANT: Do not call the same function repeatedly unless the user specifically asks for more.**`;

        // Always prepend system prompt
        if (!convo.find((m) => m.role === 'system')) {
            convo = [
                { role: 'system', content: systemPrompt },
                ...convo,
            ];
        }

        while (turns < 3 && !done) {
            const response = await openai.chat.completions.create({
                model: 'gpt-4o-mini',
                messages: convo,
                functions: allFunctions, // Use the combined functions array
                function_call: 'auto',
                temperature: 0.2,
                max_tokens: 600,
            });
            const message = response.choices[0].message;
            turns++;
            console.log(`AI message (turn ${turns}):`, JSON.stringify(message, null, 2));

            // If tool_calls (function calls), handle the first one and break
            if (message.tool_calls && message.tool_calls.length > 0) {
                for (const call of message.tool_calls) {
                    const { name, arguments: args } = call.function;
                    let data: any = null;
                    try {
                        const parsed = JSON.parse(args);
                        if (name === 'show_providers') data = parsed.providers;
                        else if (name === 'show_appointments') data = parsed.appointments;
                        else if (name === 'show_claims') data = parsed.claims;
                        else if (name === 'show_plan_progress') data = parsed.planProgressData;
                        else if (name === 'show_telehealth_time_picker') data = parsed;
                        else if (name === 'show_telehealth_appointment_card') data = parsed.appointmentDetails;
                    } catch {}
                    responseBlocks.push({ type: 'function', functionName: name, data });
                    // Append function result as a message for another turn
                    convo.push({
                        role: 'tool', // Use 'tool' role for tool_calls
                        tool_call_id: call.id, // Include tool_call_id
                        name,
                        content: JSON.stringify({ [name]: data }),
                    });
                }
                // Continue the loop to potentially get a text response after tool calls
                continue;
            }
            // Handle OpenAI v0 function_call format
            // Handle OpenAI v0 function_call format (legacy)
            if (message.function_call) {
                const { name, arguments: args } = message.function_call;
                let data: any = null;
                try {
                    const parsed = JSON.parse(args);
                    if (name === 'show_providers') data = parsed.providers;
                    else if (name === 'show_appointments') data = parsed.appointments;
                    else if (name === 'show_claims') data = parsed.claims;
                    else if (name === 'show_plan_progress') data = parsed.planProgressData;
                    else if (name === 'show_telehealth_time_picker') data = parsed;
                    else if (name === 'show_telehealth_appointment_card') data = parsed.appointmentDetails;
                } catch {}
                responseBlocks.push({ type: 'function', functionName: name, data });
                // Append function result as a message for another turn
                convo.push({
                    role: 'function',
                    name,
                    content: JSON.stringify({ [name]: data }),
                });
                // Continue the loop to potentially get a text response after function calls
                continue;
            }
            // If content, add as a text block and finish
            // If content, add as a text block and finish
            if (message.content && message.content.trim()) {
                responseBlocks.push({ type: 'text', text: message.content });
                done = true;
                break;
            }
            // If neither, break
            break;
        }

        // Compose response: if both, return both; else return whichever exists
        // Compose response: return the array of blocks
        if (responseBlocks.length > 0) {
            return NextResponse.json(responseBlocks);
        } else {
            return NextResponse.json({ error: 'No response generated.' }, { status: 500 });
        }
    } catch (e: any) {
        console.error('Error in /api/ai-assistant:', e);
        return NextResponse.json({ error: e.message || 'Internal error' }, { status: 500 });
    }
} 