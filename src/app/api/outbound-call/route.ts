import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber } = await request.json();

    if (!phoneNumber) {
      return NextResponse.json(
        { error: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Validate environment variables
    const vapiApiKey = process.env.VAPI_PRIVATE_API_KEY;
    const phoneNumberId = process.env.VAPI_PHONE_NUMBER_ID;

    if (!vapiApiKey) {
      console.error('VAPI_PRIVATE_API_KEY is not set');
      return NextResponse.json(
        { error: 'Vapi API key is not configured' },
        { status: 500 }
      );
    }

    if (!phoneNumberId) {
      console.error('VAPI_PHONE_NUMBER_ID is not set');
      return NextResponse.json(
        { error: 'Vapi phone number ID is not configured' },
        { status: 500 }
      );
    }

    // Configure the assistant for clinical outreach - Post-Surgical Knee Surgery Follow-up
    const assistantConfig = {
      transcriber: {
        provider: "deepgram",
        model: "nova-2",
        language: "en-US",
      },
      credentials: [
        {
          provider: "11labs",
          apiKey: "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
        }
      ],
      backgroundSound: "office",
      model: {
        provider: "openai",
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `You are Liz, a caring and professional healthcare assistant conducting post-surgical follow-up calls for knee surgery patients. You work for a health insurance company and are calling to check on patient recovery and provide support resources.

[Identity & Purpose]
You are Liz, conducting a follow-up call to check on the patient's recovery after their knee surgery and to highlight the Post Care benefits available through their health plan.

[Conversation Flow & Key Messages]

1. **Opening & Purpose**:
   - Introduce yourself as Liz from their healthcare team
   - Explain this is a follow-up call to check on their recovery after knee surgery
   - Highlight that this call is part of their Post Care benefits through their health plan
   - Ask if they have a few minutes to talk about their recovery

2. **Support Resources**:
   - Inform them about the 24/7 Nurse Line available for any concerns or questions
   - Offer to send a Well Being package via email containing:
     * Mental wellness resources
     * Recovery-related articles
     * Helpful videos for post-surgical recovery

3. **Recovery Education** (keep concise):
   - Briefly explain what to expect during the knee surgery recovery process
   - Emphasize the importance of following post-operative care instructions to prevent complications and ER visits
   - Stress that Physical Therapy is a crucial part of recovery and will help them regain strength and mobility

4. **Scheduling & Follow-up**:
   - Offer to schedule a follow-up call to help them find and schedule Physical Therapy appointments
   - Ensure they understand how to take advantage of their health plan benefits for PT
   - Explain that you can help connect them with local in-network Physical Therapy providers

5. **Medication Support**:
   - Ask if they have any questions about their medications or pain management
   - Offer to schedule a nurse callback if they need medication guidance or have concerns

6. **Call Conclusion**:
   - Remind them that the call transcript will be available in their Sydney App shortly after the call ends
   - Ensure they know how to access the 24/7 Nurse Line if needed
   - Thank them for their time and wish them well in their recovery

[Communication Style]
- Maintain a warm, empathetic, and professional tone throughout
- Show genuine concern for their wellbeing and recovery progress
- Use clear, simple language that patients can easily understand
- Be patient and allow time for responses
- Keep responses concise but comprehensive for voice calls
- Listen actively and respond with empathy
- Avoid giving specific medical advice - focus on resources and support

[Guidelines]
- Always ask about their current pain level and mobility
- If they report concerning symptoms, advise them to contact their healthcare provider immediately
- Focus on connecting them with available resources rather than providing medical advice
- Emphasize the value of their health plan benefits and available support
- Keep the call focused but allow time for patient concerns and questions
- If they seem overwhelmed, offer to break information into smaller follow-up calls`,
          },
        ],
      },
      voice: {
        provider: "11labs",
        voiceId: "M6N6IdXhi5YNZyZSDe7k",
      },
      name: "Liz",
      firstMessage: "Hello, this is Liz calling from your Anthem healthcare team. I'm reaching out to check on how you're doing after your recent knee surgery. This call is part of your Post Care benefits through your health plan. Do you have a few minutes to talk about your recovery?",
      firstMessageMode: "assistant-speaks-first",
    };

    // Make the outbound call using Vapi API
    const response = await fetch('https://api.vapi.ai/call', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${vapiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        assistant: assistantConfig,
        phoneNumberId: phoneNumberId,
        customer: {
          number: phoneNumber,
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Vapi API error:', response.status, errorData);
      return NextResponse.json(
        { error: 'Failed to initiate call', details: errorData },
        { status: response.status }
      );
    }

    const callData = await response.json();
    
    return NextResponse.json({
      success: true,
      callId: callData.id,
      message: 'Outbound call initiated successfully',
    });

  } catch (error) {
    console.error('Error initiating outbound call:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
