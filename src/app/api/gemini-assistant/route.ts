import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenAI, Type, FunctionCall, FunctionCallingConfigMode } from '@google/genai';
import { ChatResponsePart, Message, ForYouItem, EnrollInConditionManagementData } from '@/types'; // Import new interfaces
import profile from '@/app/data/profile.json';

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

const dateTime = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' });

// Function schemas adapted for Gemini
const showProvidersFunctionDeclaration = {
    name: 'show_providers',
    description: 'Show a single or list of doctors, dentists, specialists, providers, or facilities for the user/member',
    parameters: {
        type: Type.OBJECT,
        properties: {
            providers: {
                type: Type.ARRAY,
                items: {
                    type: Type.OBJECT,
                    properties: {
                        id: { type: Type.STRING },
                        name: { type: Type.STRING },
                        distance: { type: Type.STRING, description: 'make this up, e.g., 10 miles' },
                        cost: { type: Type.STRING, description: 'cost, e.g., $40, unless the user is just asking to see doctors in their care team, then it should be $0' },
                        rating: { type: Type.NUMBER },
                        reviewsCount: { type: Type.NUMBER },
                        specialty: { type: Type.STRING },
                        address: { type: Type.STRING },
                        phone: { type: Type.STRING },
                    },
                    required: ['id', 'name', 'distance', 'cost', 'rating', 'reviewsCount', 'specialty', 'address', 'phone'],
                },
            },
        },
        required: ['providers'],
    },
};

const showAppointmentsFunctionDeclaration = {
    name: 'show_appointments',
    description: 'Show a list of appointments for the member',
    parameters: {
        type: Type.OBJECT,
        properties: {
            appointments: {
                type: Type.ARRAY,
                items: {
                    type: Type.OBJECT,
                    properties: {
                        id: { type: Type.STRING },
                        provider: { type: Type.STRING },
                        date: { type: Type.STRING },
                        time: { type: Type.STRING },
                        location: { type: Type.STRING },
                        status: { type: Type.STRING },
                    },
                    required: ['id', 'provider', 'date', 'time', 'location', 'status'],
                },
            },
        },
        required: ['appointments'],
    },
};

const showClaimsFunctionDeclaration = {
    name: 'show_claims',
    description: 'Show a list of claims for the member',
    parameters: {
        type: Type.OBJECT,
        properties: {
            claims: {
                type: Type.ARRAY,
                items: {
                    type: Type.OBJECT,
                    properties: {
                        id: { type: Type.STRING },
                        date: { type: Type.STRING },
                        provider: { type: Type.STRING },
                        service: { type: Type.STRING },
                        amount: { type: Type.NUMBER },
                        status: { type: Type.STRING },
                        memberOwes: { type: Type.NUMBER },
                    },
                    required: ['id', 'date', 'provider', 'service', 'amount', 'status', 'memberOwes'],
                },
            },
        },
        required: ['claims'],
    },
};

const showPlanProgressFunctionDeclaration = {
    name: 'show_plan_progress',
    description: 'Show the member\'s current plan progress, including deductible and out-of-pocket maximum status.',
    parameters: {
        type: Type.OBJECT,
        properties: {
            planProgressData: {
                type: Type.OBJECT,
                properties: {
                    memberName: { type: Type.STRING },
                    memberDob: { type: Type.STRING },
                    planName: { type: Type.STRING },
                    deductible: {
                        type: Type.OBJECT,
                        properties: {
                            total: { type: Type.NUMBER },
                            spent: { type: Type.NUMBER },
                            remaining: { type: Type.NUMBER },
                        },
                        required: ['total', 'spent', 'remaining'],
                    },
                    outOfPocketMax: {
                        type: Type.OBJECT,
                        properties: {
                            total: { type: Type.NUMBER },
                            spent: { type: Type.NUMBER },
                            remaining: { type: Type.NUMBER },
                        },
                        required: ['total', 'spent', 'remaining'],
                    },
                },
                required: ['memberName', 'memberDob', 'planName', 'deductible', 'outOfPocketMax'],
            }
        },
        required: ['planProgressData'],
    },
};

const showTelehealthTimePickerFunctionDeclaration = {
    name: 'show_telehealth_time_picker',
    description: 'Show a telehealth appointment time picker to the user. Lets the user select a time for the telehealth appointment.',   
    parameters: {
        type: Type.OBJECT,
        properties: {}, // No specific parameters needed for the time picker itself
    },
};

const showTelehealthAppointmentCardFunctionDeclaration = {
    name: 'show_telehealth_appointment_card',
    description: 'Show a telehealth appointment confirmation card to the user with appointment details After you have gotten a time from the user with the show_telehealth_time_picker function.',
    parameters: {
        type: Type.OBJECT,
        properties: {
            appointmentDetails: {
                type: Type.OBJECT,
                properties: {
                    date: { type: Type.STRING, description: 'Date of the appointment, e.g., Today (April 10, 2025)' },
                    time: { type: Type.STRING, description: 'Time of the appointment, e.g., 10:00 am' },
                    doctorName: { type: Type.STRING, description: 'Name of the doctor, e.g., Dr. Robert Turner' },
                    specialty: { type: Type.STRING, description: 'Doctor\'s specialty, e.g., Gastroenterologist' },
                    rating: { type: Type.NUMBER, description: 'Doctor\'s rating, e.g., 4.7, always use 4.7 as the rating' },
                    reviewsCount: { type: Type.NUMBER, description: 'Number of reviews, e.g., 2391' },
                    costEstimate: { type: Type.STRING, description: '$40, always use $40 as the cost estimate' },
                },
                required: ['date', 'time', 'doctorName', 'specialty', 'rating', 'reviewsCount', 'costEstimate'],
            },
        },
        required: ['appointmentDetails'],
    },
};

const showPharmacyOrderStatusFunctionDeclaration = {
    name: 'show_pharmacy_order_status',
    description: 'Show pharmacy order status for a medication prescription',
    parameters: {
        type: Type.OBJECT,
        properties: {
            pharmacyOrderData: {
                type: Type.OBJECT,
                properties: {
                    medicationName: { type: Type.STRING, description: 'Name of the medication, e.g., Prednisolone' },
                    dosage: { type: Type.STRING, description: 'Dosage of the medication, e.g., 40mg' },
                    pharmacyName: { type: Type.STRING, description: 'Name of the pharmacy, e.g., Sunshine Pharmacy or Advanced Home Delivery is enrolled' },
                    pharmacyPhone: { type: Type.STRING, description: 'Phone number of the pharmacy, e.g., ************' },
                    costEstimate: { type: Type.STRING, description: 'Cost estimate for the medication, e.g., $30' },
                    currentStatus: {
                        type: Type.STRING,
                        description: 'Current status of the order',
                        enum: ['Received', 'In Progress', 'Out for Delivery', 'Delivered']
                    },
                },
                required: ['medicationName', 'dosage', 'pharmacyName', 'pharmacyPhone', 'costEstimate', 'currentStatus'],
            },
        },
        required: ['pharmacyOrderData'],
    },
};

const sendTextResponseFunctionDeclaration = {
    name: 'send_text_response',
    description: "Use this function to send a text response to the user. It can also include follow-up suggestions. This is the ONLY way to send text to the user, so you must use it for any text communication, including asking questions or providing information.",
    parameters: {
        type: Type.OBJECT,
        properties: {
            answer: {
                type: Type.STRING,
                description: 'The natural language text to be displayed to the user.'
            },
            suggestions: {
                type: Type.ARRAY,
                description: 'An array of 2-3 relevant suggestion objects. Always include suggestions that are clear and helpful next steps for the users journey. Even though suggestions are not required, you should always include them.',
                items: {
                    type: Type.OBJECT,
                    properties: { text: { type: Type.STRING } },
                    required: ['text']
                }
            }
        },
        required: ['answer'],
    },
};

const showPrescriptionsFunctionDeclaration = {
    name: 'show_prescriptions',
    description: 'Show a list of prescriptions for the member',
    parameters: {
        type: Type.OBJECT,
        properties: {
            prescriptions: {
                type: Type.ARRAY,
                items: {
                    type: Type.OBJECT,
                    properties: {
                        prescriptionId: { type: Type.STRING },
                        memberId: { type: Type.STRING },
                        memberName: { type: Type.STRING },
                        medicationName: { type: Type.STRING },
                        brandName: { type: Type.STRING, description: 'Brand name of the medication, can be null' },
                        dosage: { type: Type.STRING },
                        frequency: { type: Type.STRING },
                        route: { type: Type.STRING },
                        notes: { type: Type.STRING, description: 'Additional notes about the prescription' },
                        lastFillDate: { type: Type.STRING, description: 'Date of last fill, optional' },
                        daysSupply: { type: Type.NUMBER, description: 'Days supply for the prescription, optional' },
                        refillsRemaining: { type: Type.NUMBER, description: 'Number of refills remaining, optional' },
                        isEnrolledInAutoRefill: { type: Type.BOOLEAN, description: 'Whether enrolled in auto-refill, optional' },
                        isEligibleForAdvancedHomeDelivery: { type: Type.BOOLEAN, description: 'Whether eligible for home delivery, optional' },
                        isEnrolledInAdvancedHomeDelivery: { type: Type.BOOLEAN, description: 'Whether enrolled in home delivery, optional' },
                        tier: { type: Type.STRING, description: 'Prescription tier (e.g., Tier 1, Specialty), optional' },
                        priorAuthorization: {
                            type: Type.OBJECT,
                            properties: {
                                status: { type: Type.STRING, description: 'Prior authorization status' },
                                expiryDate: { type: Type.STRING, description: 'Expiry date for prior authorization, optional' },
                            },
                            description: 'Prior authorization information, optional'
                        },
                        conditionIds: {
                            type: Type.ARRAY,
                            items: { type: Type.STRING },
                            description: 'Array of condition IDs this prescription is for, optional'
                        },
                    },
                    required: ['prescriptionId', 'memberId', 'memberName', 'medicationName', 'dosage', 'frequency', 'route'],
                },
            },
        },
        required: ['prescriptions'],
    },
};

const enrollInConditionManagementFunctionDeclaration = {
    name: 'enroll_in_condition_management',
    description: 'Enrolls the member into a specific condition management program and provides new "For You" items relevant to the condition.',
    parameters: {
        type: Type.OBJECT,
        properties: {
            condition: { type: Type.STRING, description: 'The name of the condition being managed, e.g., "Crohn\'s disease".' },
            forYouItems: {
                type: Type.ARRAY,
                items: {
                    type: Type.OBJECT,
                    properties: {
                        id: { type: Type.STRING, description: 'Unique ID for the "For You" item, e.g., "crohns_diet_nutrition".' },
                        title: { type: Type.STRING, description: 'Display title for the "For You" item, e.g., "Diet and Nutrition for Crohn\'s".' },
                        initialMessage: { type: Type.STRING, description: 'The predefined initial message that starts a chat thread for this item, e.g., "What dietary changes can help with Crohn\'s disease?".' },
                    },
                    required: ['id', 'title', 'initialMessage'],
                },
                description: 'An array of new "For You" items to be added to the member\'s personalized suggestions.',
            },
        },
        required: ['condition', 'forYouItems'],
    },
};

const addAppointmentToFocusFunctionDeclaration = {
    name: 'add_appointment_to_focus',
    description: 'Adds a scheduled appointment to the member\'s Focus section in the Care Canvas. Use this ONLY for provider appointments (in-person or telehealth), NOT for dedicated nurse line calls. State that you added the appointment to their Focus section on the care canvas at some point in your response.',
    parameters: {
        type: Type.OBJECT,
        properties: {
            type: {
                type: Type.STRING,
                enum: ['telehealth', 'in-person'],
                description: 'The type of appointment: "telehealth" or "in-person".'
            },
            provider: {
                type: Type.STRING,
                description: 'The name of the healthcare provider, e.g., "Dr. Sarah Mitchell".'
            },
            specialty: {
                type: Type.STRING,
                description: 'The provider\'s specialty, e.g., "Internal Medicine", "Gastroenterology".'
            },
            date: {
                type: Type.STRING,
                description: 'The appointment date in a readable format, e.g., "Tuesday, January 14th".'
            },
            time: {
                type: Type.STRING,
                description: 'The appointment time, e.g., "2:30 PM".'
            },
        },
        required: ['type', 'provider', 'specialty', 'date', 'time'],
    },
};


const allFunctionDeclarations = [
    showProvidersFunctionDeclaration,
    showAppointmentsFunctionDeclaration,
    showClaimsFunctionDeclaration,
    showPlanProgressFunctionDeclaration,
    showTelehealthTimePickerFunctionDeclaration,
    showTelehealthAppointmentCardFunctionDeclaration,
    showPharmacyOrderStatusFunctionDeclaration,
    showPrescriptionsFunctionDeclaration,
    sendTextResponseFunctionDeclaration,
    enrollInConditionManagementFunctionDeclaration,
    addAppointmentToFocusFunctionDeclaration,
];

// Helper to convert frontend message format to Gemini's history format
// This is critical for stateless conversation management.
const convertMessagesToHistory = (messages: Message[]) => {
    const history: any[] = [];
    let currentModelParts: any[] = [];

    messages.forEach(msg => {
        // Frontend sends 'user' and 'ai', but Gemini uses 'user' and 'model'
        const role = msg.sender === 'ai' ? 'model' : 'user';

        if (role === 'user') {
            // If there were pending model parts, push them before the user message
            if (currentModelParts.length > 0) {
                history.push({ role: 'model', parts: currentModelParts });
                currentModelParts = [];
            }
            history.push({ role: 'user', parts: [{ text: msg.content as string }] });
        } else if (role === 'model') {
            // This handles AI messages which can be complex (text, function calls/results)
            if (Array.isArray(msg.content)) {
                msg.content.forEach((part: ChatResponsePart) => {
                    if (part.type === 'text') {
                        // This is text from a previous 'final_answer' call.
                        currentModelParts.push({ text: part.content });
                    } else if (part.type === 'function_call') {
                        // This is the model's action (the tool it decided to use).
                        currentModelParts.push({ functionCall: { name: part.functionName, args: part.functionArgs } });
                    } else if (part.type === 'function_result') {
                        // This is the result of a tool execution. It's sent back to the model with the 'user' role.
                        // First, push any pending model parts (like the functionCall that preceded this).
                        if (currentModelParts.length > 0) {
                            history.push({ role: 'model', parts: currentModelParts });
                            currentModelParts = [];
                        }
                        // Then, add the function response. The Gemini API expects this to come from the 'user' role.
                        history.push({
                            role: 'user', // Correctly attribute function results to the user/tool runner
                            parts: [{
                                functionResponse: {
                                    name: part.functionName,
                                    response: { result: part.functionData }
                                }
                            }]
                        });
                    }
                });
            }
        }
    });

    // After the loop, if there are any remaining model parts, push them.
    if (currentModelParts.length > 0) {
        history.push({ role: 'model', parts: currentModelParts });
    }

    return history;
};

export async function POST(req: NextRequest) {
    try {
        const { messages, transcripts } = await req.json();
        console.log("\n\n--- NEW ASSISTANT REQUEST ---");
        console.log("1. Incoming messages from client:", JSON.stringify(messages, null, 2));

        if (!messages) {
            return NextResponse.json({ error: 'No messages provided' }, { status: 400 });
        }

        let chatResponseParts: ChatResponsePart[] = [];

        let systemInstruction = `
# IDENTITY & GOAL
You are 'MyHealth AI', a world-class, proactive AI assistant for a health insurance application. Your primary goal is to provide a seamless, helpful, and empathetic user experience by anticipating the user's needs and providing clear, actionable information. You are fully integrated into the application and have all necessary member data. You are great at following the system instructions and the workflows and scenarios below.

- **Today's Date:** ${dateTime}
- **Member Profile:** ${JSON.stringify(profile.profile)}

---

# THE GOLDEN RULE OF COMMUNICATION
**CRITICAL:** The **ONLY** way you can communicate text to the user is by using the \`send_text_response\` function. You **MUST** use this for every single text response. It is your "speak to user" tool.

- **\`answer\` parameter:** This MUST contain your natural language response.
- **\`suggestions\` parameter:** You SHOULD ALWAYS provide 2-3 relevant, helpful follow-up suggestions to guide the user's journey.
`;

        if (transcripts && Array.isArray(transcripts) && transcripts.length > 0) {
            const transcriptText = transcripts.map((t: string, i: number) => `Transcript ${i + 1}:\n${t}`).join('\n---\n');
            systemInstruction += `
---

# ATTACHED TRANSCRIPTS
For additional context, the user has attached the following call transcripts. Use them to inform your answers if relevant:
---
${transcriptText}
`;
        }

        systemInstruction += `
---

# GUIDING PRINCIPLES
- **Act, Don't Simulate:** You are a real, integrated part of the application. Perform actions directly and confidently. Never mention that you are a PoC, using mock data, or simulating actions.
- **Multi-Tool Efficiency:** Use as many different tools as needed in a single turn to comprehensively answer a user's request.
- **Data Fidelity:** If a user requests a specific number of items (e.g., "show me two providers"), return exactly that number.
- **Proactivity is Key:** Always think one step ahead. Use the member's profile and conversation context to anticipate needs. Your suggestions should reflect this.

---

# KEY WORKFLOWS & SCENARIOS
You must follow these instructions precisely for the given scenarios.

## Personalized Condition Management Onboarding
This is a specific, multi-step scenario. Follow it precisely.

### Step 1: Initial Outreach
- **Trigger:** When the user's prompt is "Begin Personalized Condition Management Scenario".
- **Action:** You will initiate the conversation with the following empathetic outreach. Your goal is to be helpful, not intrusive.
- **Your Response (use \`send_text_response\`):**
    - **\`answer\`:** "Hello [Member's First Name]. It looks like there are some more ways to ensure you're getting the most out of your plan, I noticed that you may be eligible for our 'Personalized Condition Management'. It's a dedicated feature of your plan that we offer to members managing chronic conditions like Crohn's disease, providing specialized support at no extra cost. To unlock these benefits for you, could you please confirm if you are currently managing a Crohn's diagnosis?"
    - **\`suggestions\`:** [
        { "text": "Yes, I am managing Crohn's disease" },
        { "text": "No, I am not managing Crohn's disease" }
    ]

### Step 2: Detailing the Personalized Condition Management.
- **Trigger:** If the user confirms their diagnosis or asks for more information after your initial outreach.
- **Action:** First, provide a brief, helpful explanation of the condition. Then, clearly list the features. Conclude with a direct offer to get them started.
- **Your Response (use \`send_text_response\`):**
    - **\`answer\`:** "Thank you for confirming. I'm glad I can connect you with this part of your health plan. First, a little background: **Crohn's disease** is a type of inflammatory bowel disease (IBD) that causes inflammation of your digestive tract. Managing it is a journey, and that's exactly what Personalized Condition Management is designed to help you with.

    Here's what's included in your Personalized Condition Management for Crohn's:

    *   **Dedicated Nurse Line:** You'll be paired with a personal Coordinator Nurse, a registered nurse specializing in Crohn's. Think of them as your dedicated health advocate who can help you navigate the healthcare system and provide guidance on lifestyle adjustments.

    *   **Intelligent Symptom Logger:** Our smart symptom logger helps you track your daily feelings. Over time, it can help detect patterns and potential flare-ups, providing you with personalized insights and proactive next steps.

    *   **Condition-Focused Telehealth:** Get easy access to telehealth appointments with specialists, including scheduling help, reminders, and a summary of key takeaways after your visit.

    *   **Integrated Medication Management:** We take the stress out of managing your prescriptions with automatic refill reminders, delivery services, and alerts for potential drug interactions.

    *   **Proactive Care Team Integration:** This feature intelligently connects the dots between your symptoms and your providers, helping you know the best time to connect with your care team.

    This entire feature is now available to you. A great first step is to get you connected with your personal Coordinator Nurse. **Would you like me to enroll you in the Personalized Condition Management now?**"
    - **\`suggestions\`:** [
        { "text": "Yes, enroll me now" },
        { "text": "Remind me in 3 days" },
        { "text": "Tell me more about the benefits" }
    ]

### Step 3: Enrollment & New For You Items
- **Trigger:** When the user responds affirmatively to enrolling in "Personalized Condition Management" (e.g., "Yes, enroll me", "Enroll me now", "Sign me up").
- **Action:**
    1.  Call the \`enroll_in_condition_management\` function.
        - **\`condition\`:** "Crohn's disease"
        - **\`forYouItems\`:**
            - Item 1: \`id: 'crohns_symptom_tracking'\`, \`title: 'Track Your Crohn\'s Symptoms'\`, \`initialMessage: 'I\'d like to start tracking my Crohn\'s symptoms. Can you help me get started?'\`
            - Item 2: \`id: 'crohns_coordinator_nurse'\`, \`title: 'Meet Your Coordinator Nurse'\`, \`initialMessage: 'I\'d like to speak with a Coordinator Nurse about managing my Crohn\'s disease.'\`
            - Item 3: \`id: 'crohns_medication_management'\`, \`title: 'Medication Management'\`, \`initialMessage: 'I want to set up automatic refills and reminders for my Crohn\'s medications.'\`
            - Item 4: \`id: 'crohns_telehealth_specialist'\`, \`title: 'Schedule Specialist Telehealth'\`, \`initialMessage: 'I need to schedule a telehealth appointment with a Crohn\'s specialist.'\`
    2.  Welcome them to Personalized Condition Management. Offer to provide more details on the features, listing and concisely explaining them again but in a different way than the initial outreach.


## Telehealth Follow-up
When a user confirms a telehealth appointment time (after you've used \`show_telehealth_time_picker\`), you **MUST** perform all of the following actions in your **next single response**:
1.  **Acknowledge** the booking with a confirmation message.
2.  **Show the member's plan progress** by calling the \`show_plan_progress\` function. Use data from the member's profile which you have access to above.
3.  **Provide 3 contextual, numbered questions** for the user to ask their doctor, based on their profile (e.g., "1. How does my diet affect my Crohn's symptoms?").
Note: This only applies to telehealth appointments, not nurse calls.

## "Fast Forward" Post-Appointment
When you receive a prompt containing "Member has joined the telehealth appointment. ...Fast Foward", you **MUST** perform all of the following actions in a **single response**:
1.  **Welcome the user back** from their appointment.
2.  **Announce their new prescription** make up one that is contextual to their profile or the conversation(e.g., "Your doctor has prescribed Prednisolone for your Crohn's.").
3.  **Show the pharmacy order status** for this new prescription using \`show_pharmacy_order_status\`. The status must be "In Progress" and the cost must be "$30". Make up other details here as well. 
4.  **Announce the care team update** (e.g., "I've also updated your care team with Dr. Turner's information.").
5.  **Proactively promote and offer** to set up "Advanced Home Delivery" and auto-refills for this new prescription.

## Symptom Tracking and logging
When a user asks track or log their symptoms, simply collect the symptoms information and respond that you have logged them. Also, if the symptoms appear to be related to a condition in the member's profile, find a way to suggest a telehealth appointment with a specialist within their care team if there is one related to the condition and help them schedule it.

## Appointment Scheduling - Type Clarification
When a user requests to schedule an appointment with a provider but does not specify whether they want an in-person or telehealth appointment, you MUST ask them to clarify their preference before proceeding. Use suggestions to make it easy for them to choose:

**Example Response:**
"I'd be happy to help you schedule an appointment with [Provider Name]. Would you prefer an in-person visit at their office or a telehealth appointment from the comfort of your home?"

**Suggestions:**
- "In-person appointment"
- "Telehealth appointment"
- "What's the difference?"

Only proceed with scheduling once the user has clearly indicated their preference for either in-person or telehealth.

## In-Person Appointment Scheduling
When a user requests to schedule an in-person appointment with a provider from their care team:
1. **Acknowledge the request** and confirm the provider they want to see
2. **Simulate the scheduling process** conversationally - you do not need to use any special tools or functions for in-person appointments
3. **Provide realistic appointment options** (e.g., "I can offer you Tuesday, January 14th at 2:30 PM or Thursday, January 16th at 10:15 AM")
4. **Confirm the appointment** once the user selects a time
5. **Provide helpful next steps** such as:
   - Appointment confirmation details
   - Reminder about bringing insurance card and ID
   - Any prep instructions if relevant to their condition
   - Office location and parking information

**Important:** In-person appointment scheduling is handled conversationally without function calls, unlike telehealth appointments which use specific tools. Treat this as a natural conversation where you're helping them book directly with the provider's office.

## Prior Authorization Steerage
This is a specific workflow for helping members find cost-effective alternatives for approved prior authorizations.

### Step 1: Initial Acknowledgment and Information
- **Trigger:** When the user's prompt is "Begin Prior Authorization Steerage".
- **Action:** Acknowledge the approved prior authorization and inform about cost-saving alternatives.
- **Your Response (use \`send_text_response\`):**
    - **\`answer\`:** "Great news! I can confirm that your prior authorization for X-ray imaging at Metropolis General Hospital has been approved. However, I've identified some excellent cost-saving opportunities that could significantly reduce your out-of-pocket expenses while maintaining the same high-quality care.

    There are imaging centers closer to you that participate in your plan's preferred network, offering substantial savings. These facilities provide the same diagnostic quality but at much lower costs due to our negotiated rates.

    Would you like me to show you a list of these alternative providers with their respective costs?"
    - **\`suggestions\`:** [
        { "text": "Yes, show me the alternatives" },
        { "text": "How much can I save?" },
        { "text": "Are these centers as good as the hospital?" }
    ]

### Step 2: Present Alternative Providers
- **Trigger:** When the user asks to see alternatives or expresses interest in cost savings.
- **Action:** Use the \`show_providers\` function to display the alternative imaging centers, then provide additional context.
- **First, call \`show_providers\` with:**
    - **\`providers\`:** [
        {
            "name": "Downtown Imaging Center",
            "specialty": "Diagnostic Imaging",
            "distance": "2.3 miles",
            "rating": 4.8,
            "cost": "$10 (copay only)",
            "availability": "Same-day appointments available",
            "savings": "$240 savings vs. hospital"
        },
        {
            "name": "Suburbia Diagnostics",
            "specialty": "Diagnostic Imaging",
            "distance": "4.1 miles",
            "rating": 4.7,
            "cost": "$25 (copay + small coinsurance)",
            "availability": "Extended evening hours",
            "savings": "$225 savings vs. hospital"
        }
    ]
- **Then use \`send_text_response\`:**
    - **\`answer\`:** "I've found two excellent imaging centers in your area that can perform the same X-ray imaging with significant cost savings. Both centers are fully accredited and use state-of-the-art equipment. Your results will be sent directly to your doctor just like they would from the hospital.

    These alternative providers participate in your plan's preferred network, which is why they offer such substantial savings compared to Metropolis General Hospital.

    Would you like to switch your prior authorization to one of these providers instead?"
    - **\`suggestions\`:** [
        { "text": "Switch to Downtown Imaging ($10)" },
        { "text": "Switch to Suburbia Diagnostics ($25)" },
        { "text": "Tell me more about the quality" }
    ]

### Step 3: Confirm Provider Switch
- **Trigger:** When the user chooses to switch to one of the alternative providers.
- **Action:** Confirm the switch and explain next steps.
- **Your Response (use \`send_text_response\`):**
    - **\`answer\`:** "Perfect choice! I'm processing the change to [selected provider] right now.

    Here's what happens next:
    - I'm resubmitting your prior authorization request for [selected provider]
    - You'll receive a confirmation email within 2 hours
    - The new authorization will be valid for 30 days
    - You can schedule your appointment directly with [selected provider] using the contact information in your confirmation email

    This change will save you [savings amount] while ensuring you receive the same quality care. Your doctor will receive the results in the same timeframe as originally planned.

    Is there anything else I can help you with regarding your imaging appointment?"
    - **\`suggestions\`:** [
        { "text": "How do I schedule the appointment?" },
        { "text": "When will I get the confirmation?" },
        { "text": "Thank you, that's all I need" }
    ]
---

# FEATURE DISTINCTION - CRITICAL
## Dedicated Nurse Line vs. Personalized Condition Management

**These are TWO SEPARATE features:**

1. **Dedicated Nurse Line**: Standalone nurse support available to all members

2. **Personalized Condition Management**: Comprehensive condition management health plan feature that includes an enhanced Dedicated Nurse Line

**NEVER conflate these features.**


# DOMAIN EXPERTISE

## Pharmacy Services
You are the member's go-to guide for all pharmacy needs.
- **Benefit Inquiries:** Clearly explain drug costs and coverage using realistic data (e.g., "Ozempic is a Tier 3 drug, your copay is $45.").
- **Ordering & Refills:** Seamlessly assist with new orders and refills.
- **Proactive Management:** For chronic medications in the member's profile, proactively offer to set up auto-refills.
- **PRIORITY - Home Delivery Steerage:** Actively promote the "Advanced Home Delivery" program for both new and existing prescriptions. Explain its benefits (90-day supply, convenience, potential cost savings).

## Dedicated Nurse Line
If a request relates to a condition in the member's profile (like Crohn's), proactively offer to connect them to the Dedicated Nurse Line, explaining it provides support from a dedicated nurse. If the member wishes to enroll, you must work with them to schedule a day and time for the nurse to give them a call through conversation and using \`suggestions\` to schedule the call. Don't use any telehealth appointment functions for this, this is just a conversation based appointment setting flow.

## Focus Component - Appointment Management
When you successfully schedule an appointment with a healthcare provider (in-person or telehealth), you MUST call the \`add_appointment_to_focus\` function to add it to the member's Focus section in their Care Canvas. This applies ONLY to provider appointments, NOT to Dedicated Nurse Line calls.

**Required for Focus Component:**
- Provider appointments (in-person or telehealth)
- Include: type, provider name, specialty, date, and time
- Do NOT add Dedicated Nurse Line calls to Focus

## Medication Reminders Setup
When a user requests to setup medication reminders, follow this flow:
1. Ask if they would like reminders via SMS, push notifications, or both
2. Confirm their preference
3. Provide a simple confirmation that medication reminders have been setup

Example flow:
- User: "I'd like to setup medication reminders"
- AI: "I can help you set up medication reminders. Would you prefer to receive these reminders via SMS text messages, push notifications through the app, or both?"
- User selects preference
- AI: "Perfect! I've set up your medication reminders to be sent via [selected method]. You'll receive timely reminders for all your current medications."

## Medical Knowledge
Provide knowledgeable explanations about conditions and symptoms, referencing the user's profile. You are extremely knowledgeable and can provide vast amounts of medical advice when asked but should also steer them to a specific doctor or specialist to confirm or provide further information.
`;

        const getMockFunctionData = (name: string, args: any) => {
            switch (name) {
                case 'show_providers':
                    return args.providers;
                case 'show_appointments':
                    return args.appointments;
                case 'show_claims':
                    return args.claims;
                case 'show_plan_progress':
                    return args.planProgressData;
                case 'show_telehealth_time_picker':
                    return args;
                case 'show_telehealth_appointment_card':
                    return args.appointmentDetails;
                case 'show_pharmacy_order_status':
                    return args.pharmacyOrderData;
                case 'show_prescriptions':
                    return args.prescriptions;
                case 'enroll_in_condition_management':
                    // Return the data passed to the function so the frontend can use it
                    return args as EnrollInConditionManagementData;
                case 'add_appointment_to_focus':
                    // Return the appointment data so the frontend can add it to localStorage
                    return args;
                default:
                    return null;
            }
        };

        const history = convertMessagesToHistory(messages);
        console.log("2. Constructed history for Gemini API:", JSON.stringify(history, null, 2));

        let hasMoreTurns = true;
        let iterationCount = 0;
        const MAX_ITERATIONS = 10;

        while (hasMoreTurns && iterationCount < MAX_ITERATIONS) {
            iterationCount++;
            hasMoreTurns = false;
            console.log(`\n--- Turn ${iterationCount} ---`);

            let response = await ai.models.generateContent({
                model: "gemini-2.0-flash",
                contents: history,
                config: {
                    tools: [{ functionDeclarations: allFunctionDeclarations }],
                    systemInstruction: systemInstruction,
                    temperature: 0.1,
                    // thinkingConfig: {
                    //     thinkingBudget: 1028,
                    // },
                }
            });

            console.log(`3. Response from Gemini (Turn ${iterationCount}):`, JSON.stringify(response, null, 2));

            // Add the model's response to history for the next potential turn.
            if (response.candidates?.[0]?.content?.parts) {
                history.push({ role: 'model', parts: response.candidates[0].content.parts });
                console.log("4. History updated with model's response parts.");
            }

            // --- Corrective Action: If model returns text, force it to use a function ---
            if (response.text && (!response.functionCalls || response.functionCalls.length === 0)) {
                console.log("5x. Fallback detected. Model returned text instead of a function. Forcing a correction.");
                history.push({ role: 'model', parts: [{ text: response.text }] });

                response = await ai.models.generateContent({
                    model: "gemini-2.0-flash",
                    contents: history,
                    config: {
                        tools: [{ functionDeclarations: allFunctionDeclarations }],
                        systemInstruction: systemInstruction,
                        toolConfig: {
                            functionCallingConfig: {
                                mode: FunctionCallingConfigMode.ANY,
                                allowedFunctionNames: ['send_text_response'],
                            },
                        },
                    }
                });
                console.log(`5x. Corrective response from Gemini:`, JSON.stringify(response, null, 2));
                if (response.candidates?.[0]?.content?.parts) {
                    history.push({ role: 'model', parts: response.candidates[0].content.parts });
                }
            }

            const functionCalls = response.functionCalls;

            if (functionCalls && functionCalls.length > 0) {
                const sendTextResponseCall = functionCalls.find((call: FunctionCall) => call.name === 'send_text_response');

                if (sendTextResponseCall) {
                    console.log("5a. Detected 'send_text_response' call. Ending conversation.");
                    // --- FINAL ANSWER PATH ---
                    const { args } = sendTextResponseCall;
                    const finalText = args?.answer as string;
                    if (finalText) {
                        chatResponseParts.push({ type: 'text', content: finalText });
                    }
                    const finalSuggestions = args?.suggestions as any[];
                    if (finalSuggestions && finalSuggestions.length > 0) {
                        chatResponseParts.push({
                            type: 'function_result',
                            functionName: 'show_suggestions',
                            functionData: finalSuggestions
                        });
                    }
                    // This was the final turn.
                    hasMoreTurns = false;
                } else {
                    console.log("5b. Detected tool calls. Processing...");
                    // --- REGULAR FUNCTION CALL PATH ---
                    hasMoreTurns = true; // We need to loop again.
                    const functionResponsesForHistory = [];

                    for (const call of functionCalls) {
                        console.log(`   - Processing function: '${call.name}' with args:`, call.args);
                        const { name, args } = call;
                        if (name && args) {
                            chatResponseParts.push({ type: 'function_call', functionName: name, functionArgs: args });
                            try {
                                const data = getMockFunctionData(name, args);
                                console.log(`   - Function '${name}' executed. Result:`, data);
                                chatResponseParts.push({ type: 'function_result', functionName: name, functionData: data });
                                functionResponsesForHistory.push({
                                    functionResponse: { name, response: { result: data } }
                                });
                            } catch (funcError: any) {
                                console.error(`Error executing function ${name}:`, funcError);
                                chatResponseParts.push({ type: 'error', errorMessage: `Error executing function ${name}: ${funcError.message}` });
                                hasMoreTurns = false; // Stop on error.
                                break;
                            }
                        }
                    }

                    if (hasMoreTurns && functionResponsesForHistory.length > 0) {
                        history.push({ role: 'user', parts: functionResponsesForHistory });
                        console.log("6. History updated with function call results:", JSON.stringify(functionResponsesForHistory, null, 2));
                    } else {
                        hasMoreTurns = false;
                    }
                }
            } else if (response.text) {
                console.log("5c. No function calls detected, but also no corrective action was triggered. This is a fallback.");
                // This is a fallback if the model doesn't use final_answer.
                chatResponseParts.push({ type: 'text', content: response.text });
                hasMoreTurns = false;
            }
        }

        console.log("\n--- END OF ASSISTANT WORK ---");
        console.log("7. Final parts being sent to client:", JSON.stringify(chatResponseParts, null, 2));
        if (chatResponseParts.length > 0) {
            return NextResponse.json(chatResponseParts);
        } else {
            return NextResponse.json({ error: 'No response generated from AI.' }, { status: 500 });
        }

    } catch (e: any) {
        console.error('Error in /api/gemini-assistant:', e);
        return NextResponse.json({ error: e.message || 'Internal server error' }, { status: 500 });
    }
}