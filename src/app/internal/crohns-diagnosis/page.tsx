'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useChat } from '@/contexts/ChatContext';
import { ChevronLeft, Camera, Mic } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from '@/components/ui/button';


const CrohnsDiagnosisPage = () => {
  const router = useRouter();
  const { createThread } = useChat();

  const handleGetStarted = () => {
    const threadId = createThread({
      name: "Personalized Condition Management Journey",
      initialMessage: "Begin Personalized Condition Management Scenario",
    });
    router.push('/journeys');
  };

  return (
    <div className="light-mode-override flex items-center justify-center min-h-screen bg-white">
      <div className="w-full max-w-md md:max-w-sm bg-white rounded-lg md:rounded-[2.5rem] shadow-2xl overflow-hidden h-screen md:h-[90vh] md:max-h-[850px] flex flex-col">
        {/* iMessage Header */}
        <header className="bg-gray-50 border-b border-gray-200 p-2 flex items-center justify-between z-10">
          <button onClick={() => router.back()} className="flex items-center text-blue-500 hover:text-blue-700">
            <ChevronLeft size={32} />
            <span className="text-lg">Back</span>
          </button>
          <div className="flex flex-col items-center">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-blue-500 text-white font-bold">A</AvatarFallback>
            </Avatar>
            <span className="text-xs font-semibold mt-1">Anthem</span>
          </div>
          <div className="w-20"></div>
        </header>

        {/* Message Area */}
        <main className="flex-1 overflow-y-auto p-4 space-y-4 bg-white">
          <div className="flex items-end gap-2">
            <Avatar className="h-8 w-8 self-start">
              <AvatarFallback className="bg-blue-500 text-white font-bold">A</AvatarFallback>
            </Avatar>
            <div className="bg-gray-200 rounded-2xl p-3 max-w-[80%]">
              <p className="text-sm text-gray-800">
                Hello! We've crafted a personalized journey to help you navigate your healthcare experience. We're here to support you every step of the way.
              </p>
              <p className="text-sm text-gray-800 mt-2">
                Click below when you're ready to begin.
              </p>
              <Button 
                onClick={handleGetStarted} 
                className="mt-3 bg-blue-500 text-white rounded-lg text-sm w-full hover:bg-blue-600"
              >
                Get Started
              </Button>
            </div>
          </div>
        </main>

        {/* Fake Input Area */}
        <footer className="bg-gray-50 border-t border-gray-200 p-2">
          <div className="bg-white rounded-2xl flex items-center px-4 py-2">
            <Camera size={24} className="text-gray-400" />
            <div className="flex-1 mx-4 h-7 bg-gray-100 rounded-xl"></div>
            <Mic size={24} className="text-gray-400" />
          </div>
        </footer>
      </div>
    </div>
  );
};

export default CrohnsDiagnosisPage;
