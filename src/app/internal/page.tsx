'use client';

import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function InternalPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-white">
      <div className="mx-auto w-full max-w-sm text-center mt-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-900">Internal Use</h1>
        <p className="text-gray-600 dark:text-gray-600">
          Quick access to tailored use cases and journeys.
        </p>
        <div className="flex flex-col gap-3 mt-8 mb-20 px-4">
          <Link href="/omnichannel" passHref>
            <Button
              variant="outline"
              className="w-full flex flex-col h-auto py-4 dark:bg-white dark:border-gray-300 dark:hover:bg-gray-50 dark:text-gray-900"
            >
              Virtual Concierge - Asthma Care
              <br />
              <span className="text-xs text-gray-600 dark:text-gray-600">Chat, Voice and Transcripts</span>
            </Button>
          </Link>
          <Link href="/internal/crohns-diagnosis" passHref>
            <Button
              variant="outline"
              className="w-full flex flex-col h-auto py-4 dark:bg-white dark:border-gray-300 dark:hover:bg-gray-50 dark:text-gray-900"
            >
              Crohn's Diagnosis<br/>
              <span className="text-xs text-gray-600 dark:text-gray-600">SMS, Diagnosis Confirmation, Personalization</span>
            </Button>
          </Link>
          <Link href="/internal/prior-auth-steerage" passHref>
            <Button
              variant="outline"
              className="w-full flex flex-col h-auto py-4 dark:bg-white dark:border-gray-300 dark:hover:bg-gray-50 dark:text-gray-900"
            >
              Prior Auth and Steerage<br/>
              <span className="text-xs text-gray-600 dark:text-gray-600">Email, Benefits Awareness, Cost-Saving</span>
            </Button>
          </Link>
          <Link href="/internal/clinical-outreach" passHref>
            <Button
              variant="outline"
              className="w-full flex flex-col h-auto py-4 dark:bg-white dark:border-gray-300 dark:hover:bg-gray-50 dark:text-gray-900"
            >
              Post Procedure Clinical Outreach<br/>
              <span className="text-xs text-gray-600 dark:text-gray-600">Voice Assistant, Outbound Call, Post-Op</span>
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}