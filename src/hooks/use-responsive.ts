import { useState, useEffect } from 'react';

// Enhanced responsive breakpoints
const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
  large: 1536,
} as const;

export interface ResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLarge: boolean;
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
  isTouch: boolean;
  prefersReducedMotion: boolean;
  prefersHighContrast: boolean;
  prefersDarkMode: boolean;
}

export function useResponsive(): ResponsiveState {
  const [state, setState] = useState<ResponsiveState>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isLarge: false,
    width: 0,
    height: 0,
    orientation: 'portrait',
    isTouch: false,
    prefersReducedMotion: false,
    prefersHighContrast: false,
    prefersDarkMode: false,
  });

  useEffect(() => {
    const updateState = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setState({
        isMobile: width < BREAKPOINTS.mobile,
        isTablet: width >= BREAKPOINTS.mobile && width < BREAKPOINTS.tablet,
        isDesktop: width >= BREAKPOINTS.tablet && width < BREAKPOINTS.large,
        isLarge: width >= BREAKPOINTS.large,
        width,
        height,
        orientation: width > height ? 'landscape' : 'portrait',
        isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        prefersHighContrast: window.matchMedia('(prefers-contrast: high)').matches,
        prefersDarkMode: window.matchMedia('(prefers-color-scheme: dark)').matches,
      });
    };

    // Initial state
    updateState();

    // Resize listener
    const resizeListener = () => updateState();
    window.addEventListener('resize', resizeListener);

    // Media query listeners for user preferences
    const reducedMotionMql = window.matchMedia('(prefers-reduced-motion: reduce)');
    const highContrastMql = window.matchMedia('(prefers-contrast: high)');
    const darkModeMql = window.matchMedia('(prefers-color-scheme: dark)');

    const handlePreferenceChange = () => updateState();
    
    reducedMotionMql.addEventListener('change', handlePreferenceChange);
    highContrastMql.addEventListener('change', handlePreferenceChange);
    darkModeMql.addEventListener('change', handlePreferenceChange);

    return () => {
      window.removeEventListener('resize', resizeListener);
      reducedMotionMql.removeEventListener('change', handlePreferenceChange);
      highContrastMql.removeEventListener('change', handlePreferenceChange);
      darkModeMql.removeEventListener('change', handlePreferenceChange);
    };
  }, []);

  return state;
}

// Device-specific configuration hook
export function useDeviceConfig() {
  const responsive = useResponsive();

  return {
    // Touch targets
    touchTargetSize: responsive.isMobile ? 'h-11 w-11' : 'h-10 w-10',
    minTouchTarget: responsive.isMobile ? 'min-h-[44px]' : 'min-h-[36px]',
    
    // Text sizing
    inputTextSize: responsive.isMobile ? 'text-base' : 'text-sm',
    bodyTextSize: responsive.isMobile ? 'text-sm' : 'text-sm',
    
    // Spacing
    containerPadding: responsive.isMobile ? 'p-4' : 'p-6',
    sectionSpacing: responsive.isMobile ? 'space-y-4' : 'space-y-6',
    
    // Animation preferences
    animationDuration: responsive.prefersReducedMotion ? 'duration-0' : 'duration-200',
    transitionType: responsive.prefersReducedMotion ? 'transition-none' : 'transition-all',
    
    // Focus styles
    focusRing: responsive.prefersHighContrast ? 'focus:ring-4' : 'focus:ring-2',
    
    // Layout
    sidebarWidth: responsive.isMobile ? 'w-[280px]' : 'w-[320px]',
    maxMessageWidth: responsive.isMobile ? 'max-w-[85%]' : 'max-w-[70%]',
    
    // Performance
    shouldUseGPUAcceleration: responsive.isMobile,
    shouldPreloadImages: !responsive.isMobile,
    
    // Interaction
    hoverEffects: !responsive.isTouch,
    autoFocus: !responsive.isMobile,
    
    // Responsive state
    ...responsive,
  };
}

// Viewport utilities
export function useViewport() {
  const responsive = useResponsive();
  
  return {
    isPortrait: responsive.orientation === 'portrait',
    isLandscape: responsive.orientation === 'landscape',
    isSmallScreen: responsive.width < 640,
    isMediumScreen: responsive.width >= 640 && responsive.width < 1024,
    isLargeScreen: responsive.width >= 1024,
    aspectRatio: responsive.width / responsive.height,
    
    // Safe area calculations (for mobile devices with notches)
    hasSafeArea: responsive.isMobile && 'CSS' in window && 'supports' in window.CSS && 
                 window.CSS.supports('padding-top', 'env(safe-area-inset-top)'),
  };
}

// Performance monitoring hook
export function usePerformanceOptimizations() {
  const { isMobile, isTouch, prefersReducedMotion } = useResponsive();
  
  return {
    // Lazy loading strategies
    shouldLazyLoad: isMobile,
    intersectionThreshold: isMobile ? 0.1 : 0.3,
    
    // Animation optimizations
    shouldUseTransforms: !prefersReducedMotion,
    shouldUseOpacityTransitions: true,
    
    // Touch optimizations
    shouldUsePassiveListeners: isTouch,
    shouldDebounceScrollEvents: isMobile,
    
    // Memory optimizations
    shouldUnloadOffscreenContent: isMobile,
    maxConcurrentAnimations: isMobile ? 2 : 4,
    
    // Network optimizations
    shouldPreloadCriticalResources: !isMobile,
    shouldUseWebP: typeof window !== 'undefined' && 'createImageBitmap' in window,
  };
}