'use client';

import { useState, useEffect } from 'react';
import { ProfileData } from '@/types';

export function useProfile() {
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProfile = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/data/profile.json');
        if (!response.ok) {
          throw new Error('Failed to load profile data');
        }
        const data = await response.json();
        setProfile(data.profile);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    loadProfile();
  }, []);

  return { profile, isLoading, error };
}
