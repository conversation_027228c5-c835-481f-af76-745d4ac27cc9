# Implementation Plan: Transcripts and Chat Integration

This document outlines the implementation plan for adding a transcript viewing page and integrating transcripts into the chat view.

## 1. Data and Types

- [x] ### 1.1. Transcript Type Definition
    -   **File:** `src/types/index.ts`
    -   **Action:** Add a new `Transcript` interface to define the structure of a call transcript object.
    -   **Details:** The interface will include `id` (a unique identifier), `summary`, `transcript`, `startedAt`, `endedAt`, `endedReason`, and `recordingUrls`.

- [x] ### 1.2. Default Transcript Data
    -   **File:** `src/app/data/call_transcript.json`
    -   **Action:** This file is already provided and will be used as the default transcript data. A unique `id` will be added to each object.

## 2. Transcript Management Hook

- [x] ### 2.1. Create `use-transcripts` Hook
    -   **File:** `src/hooks/use-transcripts.ts`
    -   **Action:** Create a new custom hook to manage transcript data from local storage.
    -   **Details:**
        -   This hook will handle loading transcripts from local storage.
        -   On first load, it will check for existing transcripts. If none are found, it will load the default data from `src/app/data/call_transcript.json` and save it to local storage.
        -   It will provide a function to get all transcripts, sorted by `startedAt` date in descending order (most recent first).

## 3. Transcripts Page

- [x] ### 3.1. Create Transcripts Page
    -   **File:** `src/app/transcripts/page.tsx`
    -   **Action:** Create a new page component to display the list of transcripts.
    -   **Details:**
        -   This page will use the `use-transcripts` hook to fetch the transcript data.
        -   It will render the `TranscriptList` component, passing the sorted transcripts as a prop.
        -   The page will have a clear title, e.g., "Call Transcripts".

- [x] ### 3.2. Create `TranscriptList` Component
    -   **File:** `src/components/transcripts/TranscriptList.tsx`
    -   **Action:** Create a component to render a list of transcripts.
    -   **Details:**
        -   It will map over the transcripts and render a `TranscriptListItem` for each one.
        -   The layout will be a single-column list, designed for clarity and consistency with the app's aesthetic.

- [x] ### 3.3. Create `TranscriptListItem` Component
    -   **File:** `src/components/transcripts/TranscriptListItem.tsx`
    -   **Action:** Create a component for a single item in the transcript list.
    -   **Details:**
        -   This will be a client component using `useState` to manage its expanded/collapsed state.
        -   It will utilize the `Collapsible` component from `shadcn/ui`.
        -   **Collapsed View:** Displays the call summary, date, and time.
        -   **Expanded View:**
            -   Reveals the full transcript text within a `ScrollArea` component to ensure readability and prevent overflow.
            -   Includes a standard HTML5 `<audio>` player for playback, using the `stereo` URL from the `recordingUrls` object.
        -   The design will be clean, with clear visual cues for interaction.

## 4. Sidebar Modification

- [x] ### 4.1. Activate "View Transcripts" Button
    -   **File:** `src/components/chat/Sidebar.tsx`
    -   **Action:** Update the existing "View Transcripts" button to navigate to the new transcripts page.
    -   **Details:**
        -   Wrap the existing button with a `next/link` component pointing to `/transcripts`.
        -   Ensure clicking the button closes the sidebar on mobile devices.

## 5. Chat View Integration

- [x] ### 5.1. Update `ChatContext`
    -   **File:** `src/contexts/ChatContext.tsx`
    -   **Action:** Add state for managing selected transcripts.
    -   **Details:**
        -   Add state to hold an array of selected transcript IDs: `selectedTranscriptIds: string[]`.
        -   Add functions to `addTranscript(id: string)`, `removeTranscript(id: string)`, and `clearSelectedTranscripts()`.

- [x] ### 5.2. Create `TranscriptSelectionSheet` Component
    -   **File:** `src/components/chat/TranscriptSelectionSheet.tsx`
    -   **Action:** Create a new component to house the transcript selection UI.
    -   **Details:**
        -   This component will be triggered from `ChatView.tsx`.
        -   It will use a `Sheet` component from `shadcn/ui` that appears from the right.
        -   It will use the `use-transcripts` hook to get all available transcripts and the `useChat` hook to access the currently selected transcript IDs.
        -   It will display a list of transcripts (summary and date). Each item will have a `Checkbox` to allow the user to select or deselect it, calling the respective functions from `ChatContext`.

- [x] ### 5.3. Modify `ChatView.tsx`
    -   **File:** `src/components/chat/ChatView.tsx`
    -   **Action:** Add the "Attach Transcripts" button and integrate the `TranscriptSelectionSheet`.
    -   **Details:**
        -   Add a new button with a "paperclip" or similar icon to the left of the "Voice Assistant" button. This button will control the visibility of the `TranscriptSelectionSheet`.
        -   Import and render the `TranscriptSelectionSheet` component.
        -   Display a subtle badge on the "Attach Transcripts" button indicating the number of selected transcripts (e.g., a small number in the corner).

- [x] ### 5.4. Update API Payload
    -   **File:** `src/components/chat/ChatView.tsx`
    -   **Action:** Modify the `sendMessageToAI` function to include selected transcripts.
    -   **Details:**
        -   In `sendMessageToAI`, before making the `fetch` call, get the selected transcripts using the `useChat` and `use-transcripts` hooks.
        -   Filter the full transcript list to get the objects corresponding to `selectedTranscriptIds`.
        -   Pass the content of these transcripts as a new field, `transcripts`, in the body of the POST request to `/api/gemini-assistant`.

## 6. Backend API and Prompting Strategy

- [x] ### 6.1. Update Backend API Route
    -   **File:** `src/app/api/gemini-assistant/route.ts`
    -   **Action:** Modify the `POST` handler to accept and process the `transcripts` array.
    -   **Details:**
        -   The request body will now accept an optional `transcripts` array of strings.
        -   If this array is present and not empty, the content will be formatted and inserted into the `systemInstruction` variable.

- [x] ### 6.2. Prompting Strategy: System Prompt Injection
    -   **Rationale for Chosen Approach:** Adding transcripts to the system prompt (Approach B) was chosen over prepending them to the chat history (Approach A). This provides a clean separation between the live conversation and historical context.
        -   **Pros:** This approach is highly scalable, avoids confusing the model about conversational turn-taking (user vs. AI), and gives us precise control over how context is presented, leading to more reliable and accurate AI responses.
        -   **Cons of Alternative:** The alternative (prepending to history) risks model confusion, scales poorly with multiple long transcripts, and can lead to loss of important metadata like call dates.
    -   **Implementation:**
        -   A new section will be added to the `systemInstruction` in `route.ts`.
        -   **Example Modification:**
            ```typescript
            let systemInstruction = \`You are Liz, a friendly and knowledgeable AI health assistant...
            
            // ... existing system prompt instructions ...
    
            For additional context, the user has attached the following call transcripts. Use them to inform your answers if relevant:
            ---
            \${transcripts.map((t, i) => \`Transcript \${i + 1}:\\n\${t}\`).join('\\n---\\n')}
            ---
            \`;
            ```
This updated plan provides a clear and detailed path for implementation.