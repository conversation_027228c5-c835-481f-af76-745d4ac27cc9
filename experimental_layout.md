# Experimental Layout Implementation Guide

This document outlines the steps to implement an experimental layout feature that allows switching between the default application view and a new layout featuring the `CareCanvas` component with a chat drawer.

## 1. Create the Layout Context

First, we need a React context to manage the state of the layout switch.

**File:** `src/contexts/LayoutContext.tsx`

```tsx
"use client";
import React, { createContext, useState, useContext, ReactNode } from 'react';

interface LayoutContextType {
  isExperimentalLayout: boolean;
  toggleLayout: () => void;
}

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

export const LayoutProvider = ({ children }: { children: ReactNode }) => {
  const [isExperimentalLayout, setIsExperimentalLayout] = useState(false);

  const toggleLayout = () => {
    setIsExperimentalLayout(prev => !prev);
  };

  return (
    <LayoutContext.Provider value={{ isExperimentalLayout, toggleLayout }}>
      {children}
    </LayoutContext.Provider>
  );
};

export const useLayout = () => {
  const context = useContext(LayoutContext);
  if (context === undefined) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  return context;
};
```

## 2. Update the Root Layout

Next, modify the root layout file to incorporate the `LayoutProvider` and the logic for switching between layouts.

**File:** `src/app/layout.tsx`

The original `RootLayout` is wrapped with the `LayoutProvider`. A new component, `LayoutContent`, is created to handle the conditional rendering of the two different layouts.

The `LayoutContent` component uses the `useLayout` hook to access the `isExperimentalLayout` state and the `toggleLayout` function. A `Switch` component is added to the top right of the screen to toggle between the layouts.

### Key Changes in `src/app/layout.tsx`:

- **Import necessary components:** `LayoutProvider`, `useLayout`, `Switch`, `CareCanvas`, `ChatView`, `Drawer`, `Button`, and `JourneysPage`.
- **Wrap the application in `LayoutProvider`:** The `RootLayout` now wraps the children in the `LayoutProvider`.
- **Create `LayoutContent` component:** This component contains the logic for rendering the correct layout based on the `isExperimentalLayout` state.
- **Implement the experimental layout:**
    - When `isExperimentalLayout` is `true`, the layout displays the `CareCanvas` component.
    - A `Drawer` component is used to house the chat functionality, which includes the `AppSidebar` and the `JourneysPage`.
    - A `Button` is provided to trigger the `Drawer`.
- **Keep the original layout:** When `isExperimentalLayout` is `false`, the original layout with the `SidebarProvider` and `AppSidebar` is rendered.

Here is the complete updated code for `src/app/layout.tsx`:

```tsx
"use client";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ChatProvider } from "@/contexts/ChatContext";
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/components/ThemeProvider";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/chat/Sidebar";
import { CustomSidebarTrigger } from "@/components/ui/CustomSidebarTrigger";
import { StagewiseToolbar } from "@stagewise/toolbar-next";
import { PostHogProvider } from "@/app/posthog/posthog-provider";
import { LayoutProvider, useLayout } from "@/contexts/LayoutContext";
import { Switch } from "@/components/ui/switch";
import { CareCanvas } from "@/components/care-canvas/CareCanvas";
import { ChatView } from "@/components/chat/ChatView";
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import React from "react";
import JourneysPage from "./journeys/page";

const inter = Inter({ subsets: ["latin"] });
const stagewiseConfig = {
  plugins: [],
};

// export const metadata: Metadata = {
//   title: "Virtual Concierge",
//   description: "The Future of Healthcare",
// };

function LayoutContent({ children }: { children: React.ReactNode }) {
  const { isExperimentalLayout, toggleLayout } = useLayout();

  return (
    <>
      <div className="absolute top-4 right-4 z-50 flex items-center space-x-2">
        <Switch
          id="layout-switch"
          checked={isExperimentalLayout}
          onCheckedChange={toggleLayout}
        />
        <label htmlFor="layout-switch">Experimental Layout</label>
      </div>
      {isExperimentalLayout ? (
        <div className="relative h-screen w-screen">
          <CareCanvas />
          <Drawer>
            <DrawerTrigger asChild>
              <Button variant="outline" className="absolute bottom-4 right-4">
                Open Chat
              </Button>
            </DrawerTrigger>
            <DrawerContent>
              <div className="h-[80vh]">
                <SidebarProvider>
                  <AppSidebar />
                  <CustomSidebarTrigger />
                  <main className="flex-grow w-full overflow-auto bg-genui">
                    <JourneysPage />
                  </main>
                </SidebarProvider>
              </div>
            </DrawerContent>
          </Drawer>
        </div>
      ) : (
        <SidebarProvider>
          <AppSidebar />
          <CustomSidebarTrigger />
          <main className="flex-grow w-full overflow-auto bg-genui">
            {children}
            <Toaster />
            {process.env.NODE_ENV === "development" && (
              <StagewiseToolbar config={stagewiseConfig} />
            )}
          </main>
        </SidebarProvider>
      )}
    </>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          disableTransitionOnChange
        >
          <PostHogProvider>
            <ChatProvider>
              <LayoutProvider>
                <LayoutContent>{children}</LayoutContent>
              </LayoutProvider>
            </ChatProvider>
          </PostHogProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

## 3. Run the Application

To see the changes, run the development server:

```bash
pnpm dev
```

You should now see a switch on the top right of the page that allows you to toggle between the original and the new experimental layout.
