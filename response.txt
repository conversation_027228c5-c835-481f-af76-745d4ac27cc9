Here is the response from terminal: 
Incoming messages: [
  {
    "role": "user",
    "content": "Which doctors are in my Care Team?"
  }
]
Incoming messages: [
  {
    "role": "user",
    "content": "Which doctors are in my Care Team?"
  }
]
Gemini first response: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "functionCall": {
              "name": "show_providers",
              "args": {
                "providers": [
                  {
                    "distance": "5 miles",
                    "name": "Dr. <PERSON>",
                    "cost": "$0",
                    "address": "Indianapolis, IN",
                    "specialty": "Family Medicine",
                    "id": "PCP-001",
                    "rating": 4.8,
                    "phone": "(*************",
                    "reviewsCount": 1200
                  },
                  {
                    "cost": "$0",
                    "distance": "8 miles",
                    "rating": 4.9,
                    "name": "<PERSON>. <PERSON>",
                    "reviewsCount": 950,
                    "id": "GI-001",
                    "address": "Indianapolis, IN",
                    "specialty": "Gastroenterology",
                    "phone": "(*************"
                  },
                  {
                    "rating": 4.7,
                    "phone": "(*************",
                    "distance": "6 miles",
                    "address": "Indianapolis, IN",
                    "cost": "$0",
                    "specialty": "Psychiatry",
                    "id": "PSY-001",
                    "reviewsCount": 800,
                    "name": "Dr. <PERSON>"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 4961,
    "candidatesTokenCount": 267,
    "totalTokenCount": 5334,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 4961
      }
    ],
    "thoughtsTokenCount": 106
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini first response: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "functionCall": {
              "name": "show_providers",
              "args": {
                "providers": [
                  {
                    "address": "Indianapolis, IN",
                    "phone": "(*************",
                    "rating": 4.9,
                    "distance": "0.05 miles",
                    "name": "Dr. Sarah Mitchell",
                    "specialty": "Family Medicine",
                    "cost": "$0",
                    "id": "PCP-001",
                    "reviewsCount": 1200
                  },
                  {
                    "phone": "(*************",
                    "distance": "5.2 miles",
                    "id": "GI-001",
                    "cost": "$0",
                    "address": "Indianapolis, IN",
                    "reviewsCount": 850,
                    "rating": 4.8,
                    "specialty": "Gastroenterology",
                    "name": "Dr. Michael Chen"
                  },
                  {
                    "reviewsCount": 600,
                    "distance": "3.1 miles",
                    "id": "PSY-001",
                    "specialty": "Psychiatry",
                    "cost": "$0",
                    "address": "Indianapolis, IN",
                    "name": "Dr. Rachel Thompson",
                    "phone": "(*************",
                    "rating": 4.7
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 4961,
    "candidatesTokenCount": 274,
    "totalTokenCount": 5482,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 4961
      }
    ],
    "thoughtsTokenCount": 247
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "Here are the doctors in your Care Team:\n\n*   **Dr. Sarah Mitchell**\n    *   Specialty: Family Medicine\n    *   Practice: Meridian Primary Care\n    *   Location: Indianapolis, IN\n    *   Phone: (*************\n    *   Rating: 4.8 (1200 Reviews)\n    *   Distance: 5 miles\n    *   Cost: $0\n\n*   **Dr. Michael Chen**\n    *   Specialty: Gastroenterology\n    *   Practice: Gastroenterology Specialists of Indiana\n    *   Location: Indianapolis, IN\n    *   Phone: (*************\n    *   Rating: 4.9 (950 Reviews)\n    *   Distance: 8 miles\n    *   Cost: $0\n\n*   **Dr. Rachel Thompson**\n    *   Specialty: Psychiatry\n    *   Practice: Mindful Health Associates\n    *   Location: Indianapolis, IN\n    *   Phone: (*************\n    *   Rating: 4.7 (800 Reviews)\n    *   Distance: 6 miles\n    *   Cost: $0\n"
          },
          {
            "functionCall": {
              "name": "show_suggestions",
              "args": {
                "suggestions": [
                  {
                    "text": "Schedule an appointment with Dr. Chen"
                  },
                  {
                    "text": "Tell me more about Dr. Mitchell"
                  },
                  {
                    "text": "Who is my primary care provider?"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 5496,
    "candidatesTokenCount": 329,
    "totalTokenCount": 6114,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 5496
      }
    ],
    "thoughtsTokenCount": 289
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "I have already provided the list of doctors in your Care Team. Is there anything else I can help you with regarding your care team or anything else?"
          },
          {
            "functionCall": {
              "name": "show_suggestions",
              "args": {
                "suggestions": [
                  {
                    "text": "Schedule an appointment with Dr. Chen"
                  },
                  {
                    "text": "Tell me more about Dr. Mitchell"
                  },
                  {
                    "text": "Who is my primary care provider?"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 5874,
    "candidatesTokenCount": 79,
    "totalTokenCount": 6015,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 5874
      }
    ],
    "thoughtsTokenCount": 62
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "Here are the doctors in your Care Team:\n\n*   **Dr. Sarah Mitchell** (Family Medicine)\n    *   Meridian Primary Care, Indianapolis, IN\n    *   Phone: (*************\n    *   Cost: $0\n    *   Rating: 4.9 (1200 reviews)\n    *   Distance: 0.05 miles\n*   **Dr. Michael Chen** (Gastroenterology)\n    *   Gastroenterology Specialists of Indiana, Indianapolis, IN\n    *   Phone: (*************\n    *   Cost: $0\n    *   Rating: 4.8 (850 reviews)\n    *   Distance: 5.2 miles\n*   **Dr. Rachel Thompson** (Psychiatry)\n    *   Mindful Health Associates, Indianapolis, IN\n    *   Phone: (*************\n    *   Cost: $0\n    *   Rating: 4.7 (600 reviews)\n    *   Distance: 3.1 miles"
          },
          {
            "functionCall": {
              "name": "show_suggestions",
              "args": {
                "suggestions": [
                  {
                    "text": "Schedule an appointment with Dr. Mitchell"
                  },
                  {
                    "text": "Tell me more about Dr. Chen"
                  },
                  {
                    "text": "What conditions does my care team manage?"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 5510,
    "candidatesTokenCount": 307,
    "totalTokenCount": 5962,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 5510
      }
    ],
    "thoughtsTokenCount": 145
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "I have already provided the list of doctors in your Care Team in my previous response. Would you like me to show them again, or is there something else I can help you with?"
          },
          {
            "functionCall": {
              "name": "show_suggestions",
              "args": {
                "suggestions": [
                  {
                    "text": "Schedule an appointment with Dr. Chen"
                  },
                  {
                    "text": "Tell me more about Dr. Mitchell"
                  },
                  {
                    "text": "Who is my primary care provider?"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 6002,
    "candidatesTokenCount": 86,
    "totalTokenCount": 6134,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 6002
      }
    ],
    "thoughtsTokenCount": 46
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "Your care team manages a variety of conditions. Here's a breakdown:\n\n*   **Dr. Sarah Mitchell** (Family Medicine) manages:\n    *   Iron-Deficiency Anemia\n    *   Osteopenia\n*   **Dr. Michael Chen** (Gastroenterology) manages:\n    *   Crohn's Disease\n    *   Irritable Bowel Syndrome (IBS)\n*   **Dr. Rachel Thompson** (Psychiatry) manages:\n    *   Generalized Anxiety Disorder (GAD)\n    *   Mild Depression\n\nWould you like to know more about any of these conditions, or perhaps schedule an appointment with one of your care team members?"
          },
          {
            "functionCall": {
              "name": "show_suggestions",
              "args": {
                "suggestions": [
                  {
                    "text": "Tell me more about Crohn's Disease"
                  },
                  {
                    "text": "Schedule an appointment with Dr. Chen"
                  },
                  {
                    "text": "What are the benefits of the Coordinated Care Program?"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 5867,
    "candidatesTokenCount": 197,
    "totalTokenCount": 6184,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 5867
      }
    ],
    "thoughtsTokenCount": 120
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "I have already provided the list of doctors in your Care Team in my previous response. Is there anything else I can help you with today?"
          },
          {
            "functionCall": {
              "name": "show_suggestions",
              "args": {
                "suggestions": [
                  {
                    "text": "Schedule an appointment with Dr. Chen"
                  },
                  {
                    "text": "Tell me more about Dr. Mitchell"
                  },
                  {
                    "text": "Who is my primary care provider?"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 6137,
    "candidatesTokenCount": 77,
    "totalTokenCount": 6336,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 6137
      }
    ],
    "thoughtsTokenCount": 122
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "Yes, your care team manages several conditions. Here's a summary:\n\n*   **Dr. Sarah Mitchell** (Family Medicine) helps manage your Iron-Deficiency Anemia and Osteopenia.\n*   **Dr. Michael Chen** (Gastroenterology) specializes in your Crohn's Disease and Irritable Bowel Syndrome (IBS).\n*   **Dr. Rachel Thompson** (Psychiatry) assists with your Generalized Anxiety Disorder (GAD) and Mild Depression."
          },
          {
            "functionCall": {
              "name": "show_suggestions",
              "args": {
                "suggestions": [
                  {
                    "text": "Tell me more about Crohn's Disease"
                  },
                  {
                    "text": "Schedule an appointment with Dr. Mitchell"
                  },
                  {
                    "text": "What is the Coordinated Care Program?"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 6118,
    "candidatesTokenCount": 151,
    "totalTokenCount": 6373,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 6118
      }
    ],
    "thoughtsTokenCount": 104
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "I've already provided the list of doctors in your Care Team. To reiterate, your care team includes Dr. Sarah Mitchell (Family Medicine), Dr. Michael Chen (Gastroenterology), and Dr. Rachel Thompson (Psychiatry).\n\nIs there a specific doctor you'd like to know more about, or would you like to schedule an appointment with one of them?\n"
          },
          {
            "functionCall": {
              "name": "show_suggestions",
              "args": {
                "suggestions": [
                  {
                    "text": "Schedule an appointment with Dr. Chen"
                  },
                  {
                    "text": "Tell me more about Dr. Mitchell"
                  },
                  {
                    "text": "Who is my primary care provider?"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 6263,
    "candidatesTokenCount": 127,
    "totalTokenCount": 6498,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 6263
      }
    ],
    "thoughtsTokenCount": 108
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
 POST /api/gemini-assistant 200 in 11788ms
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "functionCall": {
              "name": "show_providers",
              "args": {
                "providers": [
                  {
                    "name": "Dr. Sarah Mitchell",
                    "cost": "$0",
                    "distance": "0.05 miles",
                    "reviewsCount": 1200,
                    "phone": "(*************",
                    "id": "PCP-001",
                    "address": "Indianapolis, IN",
                    "specialty": "Family Medicine",
                    "rating": 4.9
                  },
                  {
                    "id": "GI-001",
                    "specialty": "Gastroenterology",
                    "phone": "(*************",
                    "address": "Indianapolis, IN",
                    "rating": 4.8,
                    "distance": "5.2 miles",
                    "reviewsCount": 850,
                    "cost": "$0",
                    "name": "Dr. Michael Chen"
                  },
                  {
                    "rating": 4.7,
                    "distance": "3.1 miles",
                    "phone": "(*************",
                    "reviewsCount": 600,
                    "cost": "$0",
                    "address": "Indianapolis, IN",
                    "specialty": "Psychiatry",
                    "id": "PSY-001",
                    "name": "Dr. Rachel Thompson"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 6320,
    "candidatesTokenCount": 274,
    "totalTokenCount": 6694,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 6320
      }
    ],
    "thoughtsTokenCount": 100
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.
Gemini response after function call: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "Here are the doctors in your Care Team:\n\n*   **Dr. Sarah Mitchell** (Family Medicine)\n    *   Meridian Primary Care, Indianapolis, IN\n    *   Phone: (*************\n    *   Cost: $0\n    *   Rating: 4.9 (1200 reviews)\n    *   Distance: 0.05 miles\n*   **Dr. Michael Chen** (Gastroenterology)\n    *   Gastroenterology Specialists of Indiana, Indianapolis, IN\n    *   Phone: (*************\n    *   Cost: $0\n    *   Rating: 4.8 (850 reviews)\n    *   Distance: 5.2 miles\n*   **Dr. Rachel Thompson** (Psychiatry)\n    *   Mindful Health Associates, Indianapolis, IN\n    *   Phone: (*************\n    *   Cost: $0\n    *   Rating: 4.7 (600 reviews)\n    *   Distance: 3.1 miles"
          },
          {
            "functionCall": {
              "name": "show_suggestions",
              "args": {
                "suggestions": [
                  {
                    "text": "Schedule an appointment with Dr. Mitchell"
                  },
                  {
                    "text": "Tell me more about Dr. Chen"
                  },
                  {
                    "text": "What conditions does my care team manage?"
                  }
                ]
              }
            }
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "modelVersion": "models/gemini-2.5-flash-preview-05-20",
  "usageMetadata": {
    "promptTokenCount": 6869,
    "candidatesTokenCount": 307,
    "totalTokenCount": 7229,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 6869
      }
    ],
    "thoughtsTokenCount": 53
  }
}
there are non-text parts functionCall in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.